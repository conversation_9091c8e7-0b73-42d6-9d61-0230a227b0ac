package main

import (
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/http/httptest"
	"net/http/httputil"
	"net/url"
)

func main() {
	// 从命令行参数中获取目标 URL
	targetURL := flag.String("target", "", "The target URL to proxy requests to")
	flag.Parse()

	if *targetURL == "" {
		log.Fatal("You must specify a target URL using the -target flag.")
	}

	// 解析目标 URL
	parsedTarget, err := url.Parse(*targetURL)
	if err != nil {
		log.Fatalf("Error parsing target URL: %v", err)
	}

	// 创建一个代理处理器
	proxy := httputil.NewSingleHostReverseProxy(parsedTarget)

	// 创建一个处理请求的 http.Handler
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		// 打印原始请求报文
		reqDump, err := httputil.DumpRequest(r, true)
		if err != nil {
			http.Error(w, "Failed to dump request", http.StatusInternalServerError)
			return
		}
		fmt.Printf("Request:\n%s\n", reqDump)

		// 调用代理服务处理请求
		proxy.ServeHTTP(w, r)

		// 打印原始响应报文
		// 响应是通过代理服务器获取的，需要通过捕获响应来打印
		recorder := httptest.NewRecorder()
		proxy.ServeHTTP(recorder, r)

		respDump, err := httputil.DumpResponse(recorder.Result(), true)
		if err != nil {
			http.Error(w, "Failed to dump response", http.StatusInternalServerError)
			return
		}
		fmt.Printf("Response:\n%s\n", respDump)

		// 代理响应到客户端
		for k, v := range recorder.Header() {
			w.Header()[k] = v
		}
		w.WriteHeader(recorder.Code)
		io.Copy(w, recorder.Body)
	})

	// 启动代理服务器
	log.Println("Starting proxy server on :8080...")
	err = http.ListenAndServe(":8080", nil)
	if err != nil {
		log.Fatalf("Error starting server: %v", err)
	}
}
