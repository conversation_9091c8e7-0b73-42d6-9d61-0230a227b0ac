package main

import (
	"bufio"
	"fmt"
	"io"
	"log"
	"net/url"
	"os"
	"os/exec"
	"path"
	"strings"

	"codebase.zhonganinfo.com/zainfo/cube-kits/goto-gitlab/scm"
	"codebase.zhonganinfo.com/zainfo/cube-kits/goto-gitlab/scm/provider/bitbucket"
	"codebase.zhonganinfo.com/zainfo/cube-kits/goto-gitlab/scm/provider/gitlab"

	"github.com/joho/godotenv"
	"github.com/spf13/cobra"
)

var (
	usersMapCache map[string]string
)

// goto-gitlab 命令
var rootCmd = &cobra.Command{
	Use:   "goto-gitlab",
	Short: "迁移SCM仓库的用户和项目数据到GitLab",
}

// 添加 users 子命令
var migrateUsersCmd = &cobra.Command{
	Use:   "users",
	Short: "迁移用户",
	Run: func(cmd *cobra.Command, args []string) {
		migrateProvider := initMigrateProvider()
		gitlabProvider := initGitlabProvider()
		usersMapCache = loadUsersMap()

		err := migrateUsers(migrateProvider, gitlabProvider)
		if err != nil {
			log.Fatalf("迁移用户失败: %v", err)
		}
	},
}

// 添加 projects 子命令
var migrateProjectsCmd = &cobra.Command{
	Use:   "projects",
	Short: "迁移项目",
	Run: func(cmd *cobra.Command, args []string) {
		migrateProvider := initMigrateProvider()
		gitlabProvider := initGitlabProvider()
		usersMapCache = loadUsersMap()

		skipCode, _ := cmd.Flags().GetBool("skip-code")
		err := migrateProjects(migrateProvider, gitlabProvider, skipCode)
		if err != nil {
			log.Fatalf("迁移项目失败: %v", err)
		}
	},
}

func init() {
	migrateProjectsCmd.Flags().Bool("skip-code", false, "是否跳过代码迁移")
}

func initMigrateProvider() scm.SCM {
	migrateType := os.Getenv("MIGRATE_TYPE")
	if migrateType == "" {
		log.Fatalf("环境变量[MIGRATE_TYPE]未设置")
	}

	var migrateProvider scm.SCM
	switch migrateType {
	case "bitbucket":
		url := os.Getenv("MIGRATE_BITBUCKET_URL")
		username := os.Getenv("MIGRATE_BITBUCKET_USERNAME")
		password := os.Getenv("MIGRATE_BITBUCKET_PASSWORD")
		if url == "" || username == "" || password == "" {
			log.Fatalf("环境变量[MIGRATE_BITBUCKET_URL, MIGRATE_BITBUCKET_USERNAME, MIGRATE_BITBUCKET_PASSWORD]未设置")
		}
		migrateProvider = bitbucket.New(url, username, password)
	default:
		log.Fatalf("不支持的迁移仓库类型[%s], 支持的类型['bitbucket']", migrateType)
	}
	return migrateProvider
}

func initGitlabProvider() scm.SCM {
	gitlabURL := os.Getenv("GITLAB_URL")
	gitlabToken := os.Getenv("GITLAB_TOKEN")
	if gitlabURL == "" || gitlabToken == "" {
		log.Fatalf("环境变量[GITLAB_URL, GITLAB_TOKEN]未设置")
	}
	return gitlab.New(gitlabURL, gitlabToken)
}

func main() {
	// 加载 .env 文件中的环境变量
	if err := godotenv.Load(); err != nil {
		log.Printf("加载.env环境变量文件失败, 使用系统环境变量: %v", err)
	}

	// 注册子命令
	rootCmd.AddCommand(migrateUsersCmd)
	rootCmd.AddCommand(migrateProjectsCmd)

	// 执行根命令
	if err := rootCmd.Execute(); err != nil {
		log.Fatalf("命令执行失败: %v", err)
	}
}

// 读取users.txt文件，文件中保存所有用户名及工号数据，读取后保存到map中，key为用户名，value为工号
// 文件内容示列：
// 张三,123456
func loadUsersMap() map[string]string {
	usersMap := make(map[string]string)
	file, err := os.Open("users.txt")
	if err != nil {
		log.Fatalf("failed to open users.txt file: %v", err)
	}
	defer file.Close()

	reader := bufio.NewReader(file)
	for {
		line, _, err := reader.ReadLine()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Fatalf("failed to read line from users.txt file: %v", err)
		}
		parts := string(line)
		userData := strings.Split(parts, ",")
		if len(userData) == 2 {
			usersMap[userData[0]] = userData[1]
		}
	}
	return usersMap
}

func migrateUsers(migrateProvider scm.SCM, gitlabProvider scm.SCM) error {
	fmt.Printf("开始迁移用户......\n")
	users, err := migrateProvider.Users()
	if err != nil {
		return err
	}

	for _, user := range users {
		if usersMapCache[user.Name] == "" {
			continue
		}
		user.Username = usersMapCache[user.Name]
		err := gitlabProvider.CreateUser(user)
		if err != nil {
			fmt.Printf("迁移用户[%s(%s)]失败: %v\n", user.Name, user.Username, err)
			continue
		}
		fmt.Printf("迁移用户[%s(%s)]成功\n", user.Name, user.Username)
	}
	return nil
}

func migrateProjects(migrateProvider scm.SCM, gitlabProvider scm.SCM, skipCode bool) error {
	fmt.Printf("开始迁移仓库......\n")
	groups, err := migrateProvider.Groups()
	if err != nil {
		return err
	}

	// 读取已成功迁移的项目
	successProjects := loadSuccessProjects()

	for _, group := range groups {
		// 创建组
		if err = gitlabProvider.CreateGroup(group); err != nil {
			if strings.Contains(err.Error(), "已经被使用") {
				fmt.Printf("组[%s]已存在\n", group.Path)
			} else {
				fmt.Printf("迁移组[%s]失败: %v\n", group.Path, err)
				return err
			}
		} else {
			fmt.Printf("迁移组[%s]成功\n", group.Path)
		}

		members, err := migrateProvider.GroupMember(group.Path)
		if err != nil {
			return err
		}
		for _, member := range members {
			// 添加组用户
			if usersMapCache[member.Name] == "" {
				continue
			}
			member.Username = usersMapCache[member.Name]
			if err = gitlabProvider.AddGroupMember(group.Path, member); err != nil {
				fmt.Printf("迁移组[%s]用户[%s(%s)]失败: %v\n", group.Path, member.Name, member.Username, err)
				return err
			}
			fmt.Printf("迁移组[%s]用户[%s(%s)]成功\n", group.Path, member.Name, member.Username)
		}

		projects, err := migrateProvider.Projects(group.Path)
		if err != nil {
			return err
		}
		for _, project := range projects {
			projectPath := fmt.Sprintf("%s/%s", project.Group, project.Path)
			// 检查项目是否已经成功迁移
			if successProjects[projectPath] {
				fmt.Printf("组[%s]项目[%s]已成功迁移，跳过\n", project.Group, project.Path)
				continue
			}

			// 创建项目
			if err = gitlabProvider.CreateProjects(project); err != nil {
				if strings.Contains(err.Error(), "已经被使用") {
					fmt.Printf("组[%s]项目[%s]已存在\n", project.Group, project.Path)
					continue
				} else {
					fmt.Printf("迁移组[%s]项目[%s]失败: %v\n", project.Group, project.Path, err)
					return err
				}
			} else {
				fmt.Printf("迁移组[%s]项目[%s]成功\n", project.Group, project.Path)
			}

			members, err := migrateProvider.ProjectMember(group.Path, project.Path)
			if err != nil {
				return err
			}
			for _, member := range members {
				// 添加项目用户
				if usersMapCache[member.Name] == "" {
					continue
				}
				member.Username = usersMapCache[member.Name]
				if err = gitlabProvider.AddProjectMember(group.Path, project.Path, member); err != nil {
					fmt.Printf("迁移组[%s]项目[%s]用户[%s(%s)]失败: %v\n", group.Path, project.Path, member.Name, member.Username, err)
					return err
				}
				fmt.Printf("迁移组[%s]项目[%s]用户[%s(%s)]成功\n", group.Path, project.Path, member.Name, member.Username)
			}

			if skipCode {
				fmt.Printf("组[%s]项目[%s]，跳过代码迁移\n", group.Path, project.Path)
				continue
			}

			fmt.Printf("组[%s]项目[%s]，开始迁移代码仓库......\n", group.Path, project.Path)

			// 清理临时目录
			pwd, err := os.Getwd()
			if err != nil {
				log.Fatal(err)
			}
			repoDir := path.Join(pwd, fmt.Sprintf("%s.git", project.Path))
			os.RemoveAll(repoDir)

			// 克隆要迁移的仓库
			cloneURL := migrateProvider.CloneURLWithAuth(project.CloneURL)
			cmdClone := exec.Command("git", "clone", "--mirror", cloneURL)
			cmdClone.Dir = pwd
			fmt.Println(cmdClone.String())
			if output, err := cmdClone.CombinedOutput(); err != nil {
				fmt.Println(string(output))
				log.Fatal(err)
			}

			// 设置 GitLab 推送URL
			gitlabURL := os.Getenv("GITLAB_URL")
			remoteURL, _ := url.JoinPath(gitlabURL, project.Group, fmt.Sprintf("%s.git", project.Path))
			cmdRemote := exec.Command("git", "remote", "set-url", "origin", gitlabProvider.CloneURLWithAuth(remoteURL))
			cmdRemote.Dir = repoDir
			fmt.Println(cmdRemote.String())
			if output, err := cmdRemote.CombinedOutput(); err != nil {
				fmt.Println(string(output))
				log.Fatal(err)
			}

			// 推送到 GitLab
			cmdPush := exec.Command("git", "push", "--mirror", gitlabProvider.CloneURLWithAuth(remoteURL))
			cmdPush.Dir = repoDir
			fmt.Println(cmdPush.String())
			if output, err := cmdPush.CombinedOutput(); err != nil {
				fmt.Println(string(output))
				log.Fatal(err)
			}

			// 清理临时目录
			os.RemoveAll(repoDir)

			fmt.Printf("组[%s]项目[%s]，迁移代码仓库完成\n", group.Path, project.Path)

			// 记录成功迁移的项目
			recordSuccessProject(projectPath)
		}
	}
	return nil
}

func ExecCommand(dir, name string, arg ...string) (string, error) {
	cmd := exec.Command(name, arg...)
	cmd.Dir = dir
	out, err := cmd.CombinedOutput()
	return string(out), err
}

// 读取success.txt文件，返回已成功迁移的项目名
func loadSuccessProjects() map[string]bool {
	successProjects := make(map[string]bool)
	file, err := os.Open("success.txt")
	if err != nil {
		if os.IsNotExist(err) {
			return successProjects
		}
		log.Fatalf("failed to open success.txt file: %v", err)
	}
	defer file.Close()

	reader := bufio.NewReader(file)
	for {
		line, _, err := reader.ReadLine()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Fatalf("failed to read line from success.txt file: %v", err)
		}
		projectPath := string(line)
		successProjects[projectPath] = true
	}
	return successProjects
}

// 将成功迁移的项目名写入success.txt文件
func recordSuccessProject(projectPath string) {
	file, err := os.OpenFile("success.txt", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		log.Fatalf("failed to open success.txt file: %v", err)
	}
	defer file.Close()

	if _, err := file.WriteString(projectPath + "\n"); err != nil {
		log.Fatalf("failed to write project name to success.txt file: %v", err)
	}
}
