package scm

import (
	gogitlab "gitlab.com/gitlab-org/api/client-go"
)

const (
	InitPassword = "1qaz@WSX"
)

type User struct {
	Username string `json:"username"`
	Name     string `json:"name"`
	Email    string `json:"email"`
}

type Group struct {
	Name string `json:"name"`
	Path string `json:"path"`
	Desc string `json:"description"`
}

type Project struct {
	Group    string `json:"group"`
	Name     string `json:"name"`
	Path     string `json:"path"`
	Desc     string `json:"description"`
	CloneURL string `json:"clone_url"`
}

type Member struct {
	Username    string                    `json:"username"`
	Name        string                    `json:"name"`
	AccessLevel gogitlab.AccessLevelValue `json:"access_level"`
}

type SCM interface {
	CloneURLWithAuth(url string) string
	Users() ([]User, error)
	CreateUser(user User) error

	Groups() ([]Group, error)
	CreateGroup(groups Group) error
	GroupMember(group string) ([]Member, error)
	AddGroupMember(group string, member Member) error

	Projects(group string) ([]Project, error)
	CreateProjects(projects Project) error
	ProjectMember(group, project string) ([]Member, error)
	AddProjectMember(group, project string, member Member) error
}
