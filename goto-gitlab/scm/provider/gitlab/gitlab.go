package gitlab

import (
	"fmt"
	"net/url"
	"path"
	"strings"

	"codebase.zhonganinfo.com/zainfo/cube-kits/goto-gitlab/scm"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/tracing"
	"github.com/sirupsen/logrus"
	gogitlab "gitlab.com/gitlab-org/api/client-go"
)

type Gitlab struct {
	client *gogitlab.Client
	url    string
	token  string
}

func New(url, token string) *Gitlab {
	git, err := gogitlab.NewClient(token,
		gogitlab.WithHTTPClient(tracing.DefaultClient),
		gogitlab.WithBaseURL(fmt.Sprintf("%v/api/v4", url)))
	if err != nil {
		logrus.WithError(err).Error("gitlab client create fail")
		return &Gitlab{}
	}
	return &Gitlab{
		client: git,
		url:    url,
		token:  token,
	}
}

func convertUser(user *gogitlab.User) scm.User {
	return scm.User{
		Username: user.Username,
		Name:     user.Name,
		Email:    user.Email,
	}
}

func convertGroup(group *gogitlab.Group) scm.Group {
	return scm.Group{
		Name: group.Name,
		Path: group.Path,
		Desc: group.Description,
	}
}

func convertGroupMember(member *gogitlab.GroupMember) scm.Member {
	return scm.Member{
		Username:    member.Username,
		Name:        member.Name,
		AccessLevel: member.AccessLevel,
	}
}

func convertProject(project *gogitlab.Project) scm.Project {
	return scm.Project{
		Name:     project.Name,
		Path:     project.Path,
		Desc:     project.Description,
		Group:    project.Namespace.Path,
		CloneURL: project.HTTPURLToRepo,
	}
}

func convertProjectMember(member *gogitlab.ProjectMember) scm.Member {
	return scm.Member{
		Username:    member.Username,
		Name:        member.Name,
		AccessLevel: member.AccessLevel,
	}
}

func (g *Gitlab) CloneURLWithAuth(urlStr string) string {
	u, _ := url.Parse(urlStr)
	u.User = url.UserPassword("root", g.token)
	return u.String()
}

func (g *Gitlab) Users() ([]scm.User, error) {
	opt := &gogitlab.ListUsersOptions{}
	opt.Page = 1
	opt.PerPage = 100

	var users []scm.User

	userList, rsp, err := g.client.Users.ListUsers(opt)
	if err != nil {
		return nil, err
	}
	for _, user := range userList {
		users = append(users, convertUser(user))
	}

	for i := 2; i <= rsp.TotalPages; i++ {
		opt.Page = i
		userList, _, err = g.client.Users.ListUsers(opt)
		if err != nil {
			return nil, err
		}
		for _, user := range userList {
			users = append(users, convertUser(user))
		}
	}

	return users, nil
}

func (g *Gitlab) CreateUser(user scm.User) error {
	opt := &gogitlab.CreateUserOptions{
		Email:               gogitlab.Ptr(user.Email),
		Name:                gogitlab.Ptr(user.Name),
		Username:            gogitlab.Ptr(user.Username),
		Password:            gogitlab.Ptr(scm.InitPassword),
		SkipConfirmation:    gogitlab.Ptr(true),
		ForceRandomPassword: gogitlab.Ptr(false),
		ResetPassword:       gogitlab.Ptr(true),
	}
	u, _, err := g.client.Users.CreateUser(opt)
	if err != nil {
		return err
	}

	modifyOpt := &gogitlab.ModifyUserOptions{
		Password: gogitlab.Ptr(scm.InitPassword),
	}
	_, _, err = g.client.Users.ModifyUser(u.ID, modifyOpt)
	return err
}

func (g *Gitlab) Groups() ([]scm.Group, error) {
	opt := &gogitlab.ListGroupsOptions{}
	opt.Page = 1
	opt.PerPage = 100

	var groups []scm.Group

	groupList, rsp, err := g.client.Groups.ListGroups(opt)
	if err != nil {
		return nil, err
	}
	for _, group := range groupList {
		groups = append(groups, convertGroup(group))
	}

	for i := 2; i <= rsp.TotalPages; i++ {
		opt.Page = i
		groupList, _, err = g.client.Groups.ListGroups(opt)
		if err != nil {
			return nil, err
		}
		for _, group := range groupList {
			groups = append(groups, convertGroup(group))
		}
	}

	return groups, nil
}

func (g *Gitlab) CreateGroup(group scm.Group) error {
	opt := &gogitlab.CreateGroupOptions{
		Name:          gogitlab.Ptr(group.Name),
		Path:          gogitlab.Ptr(group.Path),
		Description:   gogitlab.Ptr(group.Desc),
		Visibility:    gogitlab.Ptr(gogitlab.PrivateVisibility),
		DefaultBranch: gogitlab.Ptr("master"),
	}
	_, _, err := g.client.Groups.CreateGroup(opt)
	return err
}

func (g *Gitlab) GroupMember(group string) ([]scm.Member, error) {
	opt := &gogitlab.ListGroupMembersOptions{}
	opt.Page = 1
	opt.PerPage = 100

	var members []scm.Member

	memberList, rsp, err := g.client.Groups.ListGroupMembers(group, opt)
	if err != nil {
		return nil, err
	}
	for _, member := range memberList {
		members = append(members, convertGroupMember(member))
	}

	for i := 2; i <= rsp.TotalPages; i++ {
		opt.Page = i
		memberList, _, err = g.client.Groups.ListGroupMembers(group, opt)
		if err != nil {
			return nil, err
		}
		for _, member := range memberList {
			members = append(members, convertGroupMember(member))
		}
	}

	return members, nil
}

func (g *Gitlab) AddGroupMember(group string, member scm.Member) error {
	listUsersOpt := &gogitlab.ListUsersOptions{}
	listUsersOpt.Page = 1
	listUsersOpt.PerPage = 1
	listUsersOpt.Username = gogitlab.Ptr(member.Username)
	users, _, err := g.client.Users.ListUsers(listUsersOpt)
	if err != nil {
		return err
	}

	opt := &gogitlab.AddGroupMemberOptions{
		UserID:      gogitlab.Ptr(users[0].ID),
		Username:    gogitlab.Ptr(member.Username),
		AccessLevel: gogitlab.Ptr(member.AccessLevel),
	}
	_, _, err = g.client.GroupMembers.AddGroupMember(group, opt)
	if err != nil {
		if strings.Contains(err.Error(), "already exists") {
			editOpt := &gogitlab.EditGroupMemberOptions{
				AccessLevel: gogitlab.Ptr(member.AccessLevel),
			}
			_, _, err = g.client.GroupMembers.EditGroupMember(group, users[0].ID, editOpt)
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}
	return nil
}

func (g *Gitlab) Projects(group string) ([]scm.Project, error) {
	groupData, _, err := g.client.Groups.GetGroup(group, &gogitlab.GetGroupOptions{WithProjects: gogitlab.Ptr(true)})
	if err != nil {
		return nil, err
	}

	var projects []scm.Project
	for _, project := range groupData.Projects {
		projects = append(projects, convertProject(project))
	}
	return projects, nil
}

func (g *Gitlab) CreateProjects(projects scm.Project) error {
	group, _, err := g.client.Groups.GetGroup(projects.Group, &gogitlab.GetGroupOptions{})
	if err != nil {
		return err
	}

	opt := &gogitlab.CreateProjectOptions{
		NamespaceID:                  gogitlab.Ptr(group.ID),
		Name:                         gogitlab.Ptr(projects.Name),
		Path:                         gogitlab.Ptr(projects.Path),
		Description:                  gogitlab.Ptr(projects.Desc),
		Visibility:                   gogitlab.Ptr(gogitlab.PrivateVisibility),
		InitializeWithReadme:         gogitlab.Ptr(false),
		AutoDevopsEnabled:            gogitlab.Ptr(false),
		RemoveSourceBranchAfterMerge: gogitlab.Ptr(false),
	}
	_, _, err = g.client.Projects.CreateProject(opt)
	return err
}

func (g *Gitlab) ProjectMember(group, project string) ([]scm.Member, error) {
	opt := &gogitlab.ListProjectMembersOptions{}
	opt.Page = 1
	opt.PerPage = 100

	var members []scm.Member

	memberList, rsp, err := g.client.ProjectMembers.ListProjectMembers(path.Join(group, project), opt)
	if err != nil {
		return nil, err
	}
	for _, member := range memberList {
		members = append(members, convertProjectMember(member))
	}

	for i := 2; i <= rsp.TotalPages; i++ {
		opt.Page = i
		memberList, _, err = g.client.ProjectMembers.ListProjectMembers(path.Join(group, project), opt)
		if err != nil {
			return nil, err
		}
		for _, member := range memberList {
			members = append(members, convertProjectMember(member))
		}
	}

	return members, nil
}

func (g *Gitlab) AddProjectMember(group, project string, member scm.Member) error {
	listUsersOpt := &gogitlab.ListUsersOptions{}
	listUsersOpt.Page = 1
	listUsersOpt.PerPage = 1
	listUsersOpt.Username = gogitlab.Ptr(member.Username)
	users, _, err := g.client.Users.ListUsers(listUsersOpt)
	if err != nil {
		return err
	}

	opt := &gogitlab.AddProjectMemberOptions{
		UserID:      gogitlab.Ptr(users[0].ID),
		Username:    gogitlab.Ptr(member.Username),
		AccessLevel: gogitlab.Ptr(member.AccessLevel),
	}
	_, _, err = g.client.ProjectMembers.AddProjectMember(path.Join(group, project), opt)
	if err != nil {
		if strings.Contains(err.Error(), "already exists") {
			editOpt := &gogitlab.EditProjectMemberOptions{
				AccessLevel: gogitlab.Ptr(member.AccessLevel),
			}
			_, _, err = g.client.ProjectMembers.EditProjectMember(path.Join(group, project), users[0].ID, editOpt)
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}
	return nil
}
