package bitbucket

import (
	"context"
	"net/url"
	"time"

	"codebase.zhonganinfo.com/zainfo/cube-kits/goto-gitlab/scm"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/basichttp"
	http2 "git.zhonganinfo.com/zainfo/shiplib/pkgs/http"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/services/bitbucket"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/services/client"
	gogitlab "gitlab.com/gitlab-org/api/client-go"
)

const (
	limit = 100
)

type Bitbucket struct {
	ctx      context.Context
	client   bitbucket.Bitbucket
	username string
	password string
}

func New(url, username, password string) *Bitbucket {
	return &Bitbucket{
		ctx:      context.Background(),
		client:   bitbucket.New(url, bitbucket.NewClient(http2.NewBasicAuthTransport(username, password, basichttp.DefaultTransport), time.Second*60)),
		username: username,
		password: password,
	}
}

func convertUser(user User) scm.User {
	return scm.User{
		Username: user.Name,
		Name:     user.DisplayName,
		Email:    user.EmailAddress,
	}
}

func convertGroup(project Project) scm.Group {
	return scm.Group{
		Name: project.Name,
		Path: project.Key,
		Desc: "",
	}
}

func convertGroupMember(member ProjectMember) scm.Member {
	return scm.Member{
		Username:    member.User.Name,
		Name:        member.User.DisplayName,
		AccessLevel: convertAccessLevel(member.Permission),
	}
}

func convertProject(repository Repository) scm.Project {
	var cloneURL string
	for _, clone := range repository.Links.Clone {
		if clone.Name == "http" {
			cloneURL = clone.Href
		}
	}
	return scm.Project{
		Name:     repository.Name,
		Path:     repository.Slug,
		Desc:     repository.Description,
		Group:    repository.Project.Key,
		CloneURL: cloneURL,
	}
}

func convertProjectMember(member RepositoryMember) scm.Member {
	return scm.Member{
		Username:    member.User.Name,
		Name:        member.User.DisplayName,
		AccessLevel: convertAccessLevel(member.Permission),
	}
}

func convertAccessLevel(permission string) gogitlab.AccessLevelValue {
	var gitlabAccessLevel gogitlab.AccessLevelValue
	switch permission {
	case "PROJECT_ADMIN", "REPO_CREATE", "REPO_ADMIN", "ADMIN", "SYS_ADMIN":
		gitlabAccessLevel = gogitlab.OwnerPermissions
	case "PROJECT_WRITE", "REPO_WRITE":
		gitlabAccessLevel = gogitlab.DeveloperPermissions
	case "PROJECT_READ", "REPO_READ":
		gitlabAccessLevel = gogitlab.ReporterPermissions
	default:
		gitlabAccessLevel = gogitlab.DeveloperPermissions
	}
	return gitlabAccessLevel
}

func (b *Bitbucket) CloneURLWithAuth(urlStr string) string {
	u, _ := url.Parse(urlStr)
	u.User = url.UserPassword(b.username, b.password)
	return u.String()
}

func (b *Bitbucket) Users() ([]scm.User, error) {
	var users []scm.User
	start := 0
	for {
		q := client.NewParam().
			WithQueries("start", start).
			WithQueries("limit", limit).
			WithQueries("avatarSize", 48)

		var rsp UsersRsp
		err := b.client.GetUsers(b.ctx, &rsp, q)
		if err != nil {
			return nil, err
		}

		for _, user := range rsp.Values {
			users = append(users, convertUser(user))
		}

		if rsp.IsLastPage || rsp.NextPageStart == 0 {
			break
		}
		start = rsp.NextPageStart
	}

	return users, nil
}

// CreateUser 不需要实现
func (b *Bitbucket) CreateUser(user scm.User) error {
	return nil
}

func (b *Bitbucket) Groups() ([]scm.Group, error) {
	var groups []scm.Group
	start := 0
	for {
		q := client.NewParam().
			WithQueries("start", start).
			WithQueries("limit", limit).
			WithQueries("avatarSize", 48)

		var rsp ProjectsRsp
		err := b.client.GetProjects(b.ctx, &rsp, q)
		if err != nil {
			return nil, err
		}

		for _, project := range rsp.Values {
			groups = append(groups, convertGroup(project))
		}

		if rsp.IsLastPage || rsp.NextPageStart == 0 {
			break
		}
		start = rsp.NextPageStart
	}
	return groups, nil
}

// CreateGroup 不需要实现
func (b *Bitbucket) CreateGroup(groups scm.Group) error {
	return nil
}

func (b *Bitbucket) GroupMember(group string) ([]scm.Member, error) {
	var members []scm.Member
	start := 0
	for {
		q := client.NewParam().
			WithQueries("start", start).
			WithQueries("limit", limit).
			WithQueries("avatarSize", 48)

		var rsp ProjectMembersRsp
		err := b.client.GetProjectPermissions(b.ctx, group, &rsp, q)
		if err != nil {
			return nil, err
		}

		for _, member := range rsp.Values {
			members = append(members, convertGroupMember(member))
		}

		if rsp.IsLastPage || rsp.NextPageStart == 0 {
			break
		}
		start = rsp.NextPageStart
	}
	return members, nil
}

// AddGroupMember 不需要实现
func (b *Bitbucket) AddGroupMember(group string, member scm.Member) error {
	return nil
}

func (b *Bitbucket) Projects(group string) ([]scm.Project, error) {
	var projects []scm.Project
	start := 0
	for {
		q := client.NewParam().
			WithQueries("start", start).
			WithQueries("limit", limit).
			WithQueries("avatarSize", 48).
			WithQueries("projectkey", group).
			WithQueries("archived", "ACTIVE")

		var rsp RepositoriesRsp
		err := b.client.GetRepositories(b.ctx, &rsp, q)
		if err != nil {
			return nil, err
		}

		for _, project := range rsp.Values {
			projects = append(projects, convertProject(project))
		}

		if rsp.IsLastPage || rsp.NextPageStart == 0 {
			break
		}
		start = rsp.NextPageStart
	}
	return projects, nil
}

// CreateProjects 不需要实现
func (b *Bitbucket) CreateProjects(projects scm.Project) error {
	return nil
}

func (b *Bitbucket) ProjectMember(group, project string) ([]scm.Member, error) {
	var members []scm.Member
	start := 0
	for {
		q := client.NewParam().
			WithQueries("start", start).
			WithQueries("limit", limit).
			WithQueries("avatarSize", 48).
			WithQueries("permission", "REPO_ADMIN").
			WithQueries("permission", "REPO_WRITE").
			WithQueries("permission", "REPO_READ")

		var rsp RepositoryMembersRsp
		err := b.client.GetRepositoryPermissions(b.ctx, group, project, &rsp, q)
		if err != nil {
			return nil, err
		}

		for _, member := range rsp.Values {
			members = append(members, convertProjectMember(member))
		}

		if rsp.IsLastPage || rsp.NextPageStart == 0 {
			break
		}
		start = rsp.NextPageStart
	}
	return members, nil
}

// AddProjectMember 不需要实现
func (b *Bitbucket) AddProjectMember(group, project string, member scm.Member) error {
	return nil
}
