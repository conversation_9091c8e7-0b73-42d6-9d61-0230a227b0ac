package bitbucket

type Page struct {
	Size          int  `json:"size"`
	Limit         int  `json:"limit"`
	IsLastPage    bool `json:"isLastPage"`
	Start         int  `json:"start"`
	NextPageStart int  `json:"nextPageStart"`
}

type User struct {
	Name           string `json:"name"`
	EmailAddress   string `json:"emailAddress"`
	Active         bool   `json:"active"`
	DisplayName    string `json:"displayName"`
	Id             int    `json:"id"`
	Slug           string `json:"slug"`
	Type           string `json:"type"`
	DirectoryName  string `json:"directoryName"`
	Deletable      bool   `json:"deletable"`
	MutableDetails bool   `json:"mutableDetails"`
	MutableGroups  bool   `json:"mutableGroups"`
	Properties     struct {
		LicenseStatus string `json:"licenseStatus"`
	} `json:"properties"`
	Links struct {
		Self []struct {
			Href string `json:"href"`
		} `json:"self"`
	} `json:"links"`
	AvatarUrl                   string `json:"avatarUrl"`
	LastAuthenticationTimestamp int64  `json:"lastAuthenticationTimestamp"`
}

type UsersRsp struct {
	Page
	Values []User `json:"values"`
}

type Project struct {
	Key    string `json:"key"`
	Id     int    `json:"id"`
	Name   string `json:"name"`
	Public bool   `json:"public"`
	Type   string `json:"type"`
	Links  struct {
		Self []struct {
			Href string `json:"href"`
		} `json:"self"`
	} `json:"links"`
	AvatarUrl string `json:"avatarUrl"`
}

type ProjectsRsp struct {
	Page
	Values []Project `json:"values"`
}

type ProjectMember struct {
	Permission string `json:"permission"`
	User       struct {
		Name         string `json:"name"`
		EmailAddress string `json:"emailAddress"`
		Active       bool   `json:"active"`
		DisplayName  string `json:"displayName"`
		Id           int    `json:"id"`
		Slug         string `json:"slug"`
		Type         string `json:"type"`
		Links        struct {
			Self []struct {
				Href string `json:"href"`
			} `json:"self"`
		} `json:"links"`
		AvatarUrl string `json:"avatarUrl"`
	} `json:"user"`
}

type ProjectMembersRsp struct {
	Page
	Values []ProjectMember `json:"values"`
}

type Repository struct {
	Slug          string `json:"slug"`
	Id            int    `json:"id"`
	Name          string `json:"name"`
	Description   string `json:"description"`
	HierarchyId   string `json:"hierarchyId"`
	ScmId         string `json:"scmId"`
	State         string `json:"state"`
	StatusMessage string `json:"statusMessage"`
	Forkable      bool   `json:"forkable"`
	Project       struct {
		Key    string `json:"key"`
		Id     int    `json:"id"`
		Name   string `json:"name"`
		Public bool   `json:"public"`
		Type   string `json:"type"`
		Links  struct {
			Self []struct {
				Href string `json:"href"`
			} `json:"self"`
		} `json:"links"`
		AvatarUrl string `json:"avatarUrl"`
	} `json:"project"`
	Public   bool `json:"public"`
	Archived bool `json:"archived"`
	Links    struct {
		Clone []struct {
			Href string `json:"href"`
			Name string `json:"name"`
		} `json:"clone"`
		Self []struct {
			Href string `json:"href"`
		} `json:"self"`
	} `json:"links"`
}

type RepositoriesRsp struct {
	Page
	Values []Repository `json:"values"`
}

type RepositoryMember struct {
	Permission string `json:"permission"`
	User       struct {
		Name         string `json:"name"`
		EmailAddress string `json:"emailAddress"`
		Active       bool   `json:"active"`
		DisplayName  string `json:"displayName"`
		Id           int    `json:"id"`
		Slug         string `json:"slug"`
		Type         string `json:"type"`
		Links        struct {
			Self []struct {
				Href string `json:"href"`
			} `json:"self"`
		} `json:"links"`
		AvatarUrl string `json:"avatarUrl"`
	} `json:"user"`
}

type RepositoryMembersRsp struct {
	Page
	Values []RepositoryMember `json:"values"`
}
