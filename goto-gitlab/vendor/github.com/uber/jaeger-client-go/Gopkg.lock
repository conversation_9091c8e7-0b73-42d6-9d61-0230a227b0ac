# This file is autogenerated, do not edit; changes may be undone by the next 'dep ensure'.


[[projects]]
  digest = "1:9f3b30d9f8e0d7040f729b82dcbc8f0dead820a133b3147ce355fc451f32d761"
  name = "github.com/BurntSushi/toml"
  packages = ["."]
  pruneopts = "UT"
  revision = "3012a1dbe2e4bd1391d42b32f0577cb7bbc7f005"
  version = "v0.3.1"

[[projects]]
  digest = "1:d6afaeed1502aa28e80a4ed0981d570ad91b2579193404256ce672ed0a609e0d"
  name = "github.com/beorn7/perks"
  packages = ["quantile"]
  pruneopts = "UT"
  revision = "37c8de3658fcb183f997c4e13e8337516ab753e6"
  version = "v1.0.1"

[[projects]]
  branch = "master"
  digest = "1:4c4c33075b704791d6a7f09dfb55c66769e8a1dc6adf87026292d274fe8ad113"
  name = "github.com/codahale/hdrhistogram"
  packages = ["."]
  pruneopts = "UT"
  revision = "3a0bb77429bd3a61596f5e8a3172445844342120"

[[projects]]
  branch = "master"
  digest = "1:a382acd6150713655ded76ab5fbcbc7924a7808dab4312dda5d1f23dd8ce5277"
  name = "github.com/crossdock/crossdock-go"
  packages = [
    ".",
    "assert",
    "require",
  ]
  pruneopts = "UT"
  revision = "049aabb0122b03bc9bd30cab8f3f91fb60166361"

[[projects]]
  digest = "1:ffe9824d294da03b391f44e1ae8281281b4afc1bdaa9588c9097785e3af10cec"
  name = "github.com/davecgh/go-spew"
  packages = ["spew"]
  pruneopts = "UT"
  revision = "8991bc29aa16c548c550c7ff78260e27b9ab7c73"
  version = "v1.1.1"

[[projects]]
  digest = "1:7ae311278f7ccaa724de8f2cdec0a507ba3ee6dea8c77237e8157bcf64b0f28b"
  name = "github.com/golang/mock"
  packages = ["gomock"]
  pruneopts = "UT"
  revision = "3a35fb6e3e18b9dbfee291262260dee7372d2a92"
  version = "v1.4.3"

[[projects]]
  digest = "1:573ca21d3669500ff845bdebee890eb7fc7f0f50c59f2132f2a0c6b03d85086a"
  name = "github.com/golang/protobuf"
  packages = ["proto"]
  pruneopts = "UT"
  revision = "6c65a5562fc06764971b7c5d05c76c75e84bdbf7"
  version = "v1.3.2"

[[projects]]
  digest = "1:ff5ebae34cfbf047d505ee150de27e60570e8c394b3b8fdbb720ff6ac71985fc"
  name = "github.com/matttproud/golang_protobuf_extensions"
  packages = ["pbutil"]
  pruneopts = "UT"
  revision = "c12348ce28de40eed0136aa2b644d0ee0650e56c"
  version = "v1.0.1"

[[projects]]
  digest = "1:727b8f567a30d0739d6c26b9472b3422b351c93cf62095164c845a54b16fc18e"
  name = "github.com/opentracing/opentracing-go"
  packages = [
    ".",
    "ext",
    "harness",
    "log",
  ]
  pruneopts = "UT"
  revision = "659c90643e714681897ec2521c60567dd21da733"
  version = "v1.1.0"

[[projects]]
  digest = "1:cf31692c14422fa27c83a05292eb5cbe0fb2775972e8f1f8446a71549bd8980b"
  name = "github.com/pkg/errors"
  packages = ["."]
  pruneopts = "UT"
  revision = "ba968bfe8b2f7e042a574c888954fccecfa385b4"
  version = "v0.8.1"

[[projects]]
  digest = "1:0028cb19b2e4c3112225cd871870f2d9cf49b9b4276531f03438a88e94be86fe"
  name = "github.com/pmezard/go-difflib"
  packages = ["difflib"]
  pruneopts = "UT"
  revision = "792786c7400a136282c1664665ae0a8db921c6c2"
  version = "v1.0.0"

[[projects]]
  digest = "1:7097829edd12fd7211fca0d29496b44f94ef9e6d72f88fb64f3d7b06315818ad"
  name = "github.com/prometheus/client_golang"
  packages = [
    "prometheus",
    "prometheus/internal",
  ]
  pruneopts = "UT"
  revision = "170205fb58decfd011f1550d4cfb737230d7ae4f"
  version = "v1.1.0"

[[projects]]
  branch = "master"
  digest = "1:2d5cd61daa5565187e1d96bae64dbbc6080dacf741448e9629c64fd93203b0d4"
  name = "github.com/prometheus/client_model"
  packages = ["go"]
  pruneopts = "UT"
  revision = "14fe0d1b01d4d5fc031dd4bec1823bd3ebbe8016"

[[projects]]
  digest = "1:f119e3205d3a1f0f19dbd7038eb37528e2c6f0933269dc344e305951fb87d632"
  name = "github.com/prometheus/common"
  packages = [
    "expfmt",
    "internal/bitbucket.org/ww/goautoneg",
    "model",
  ]
  pruneopts = "UT"
  revision = "287d3e634a1e550c9e463dd7e5a75a422c614505"
  version = "v0.7.0"

[[projects]]
  digest = "1:a210815b437763623ecca8eb91e6a0bf4f2d6773c5a6c9aec0e28f19e5fd6deb"
  name = "github.com/prometheus/procfs"
  packages = [
    ".",
    "internal/fs",
    "internal/util",
  ]
  pruneopts = "UT"
  revision = "499c85531f756d1129edd26485a5f73871eeb308"
  version = "v0.0.5"

[[projects]]
  digest = "1:ac83cf90d08b63ad5f7e020ef480d319ae890c208f8524622a2f3136e2686b02"
  name = "github.com/stretchr/objx"
  packages = ["."]
  pruneopts = "UT"
  revision = "477a77ecc69700c7cdeb1fa9e129548e1c1c393c"
  version = "v0.1.1"

[[projects]]
  digest = "1:d88ba57c4e8f5db6ce9ab6605a89f4542ee751b576884ba5271c2ba3d4b6f2d2"
  name = "github.com/stretchr/testify"
  packages = [
    "assert",
    "mock",
    "require",
    "suite",
  ]
  pruneopts = "UT"
  revision = "221dbe5ed46703ee255b1da0dec05086f5035f62"
  version = "v1.4.0"

[[projects]]
  digest = "1:5b98956718573850caf7e0fd00b571a6657c4ef1f345ddf0c96b43ce355fe862"
  name = "github.com/uber/jaeger-client-go"
  packages = [
    ".",
    "config",
    "crossdock/client",
    "crossdock/common",
    "crossdock/endtoend",
    "crossdock/log",
    "crossdock/server",
    "crossdock/thrift/tracetest",
    "internal/baggage",
    "internal/baggage/remote",
    "internal/reporterstats",
    "internal/spanlog",
    "internal/throttler",
    "internal/throttler/remote",
    "log",
    "log/zap/mock_opentracing",
    "rpcmetrics",
    "testutils",
    "thrift",
    "thrift-gen/agent",
    "thrift-gen/baggage",
    "thrift-gen/jaeger",
    "thrift-gen/sampling",
    "thrift-gen/zipkincore",
    "transport",
    "transport/zipkin",
    "utils",
  ]
  pruneopts = "UT"
  revision = "66c008c3d6ad856cac92a0af53186efbffa8e6a5"
  version = "v2.24.0"

[[projects]]
  digest = "1:0ec60ffd594af00ba1660bc746aa0e443d27dd4003dee55f9d08a0b4ff5431a3"
  name = "github.com/uber/jaeger-lib"
  packages = [
    "metrics",
    "metrics/metricstest",
    "metrics/prometheus",
  ]
  pruneopts = "UT"
  revision = "a87ae9d84fb038a8d79266298970720be7c80fcd"
  version = "v2.2.0"

[[projects]]
  digest = "1:0bdcb0c740d79d400bd3f7946ac22a715c94db62b20bfd2e01cd50693aba0600"
  name = "go.uber.org/atomic"
  packages = ["."]
  pruneopts = "UT"
  revision = "9dc4df04d0d1c39369750a9f6c32c39560672089"
  version = "v1.5.0"

[[projects]]
  digest = "1:002ebc50f3ef475ac325e1904be931d9dcba6dc6d73b5682afce0c63436e3902"
  name = "go.uber.org/multierr"
  packages = ["."]
  pruneopts = "UT"
  revision = "c3fc3d02ec864719d8e25be2d7dde1e35a36aa27"
  version = "v1.3.0"

[[projects]]
  branch = "master"
  digest = "1:3032e90a153750ea149f68bf081f97ca738f041fba45c41c80737f572ffdf2f4"
  name = "go.uber.org/tools"
  packages = ["update-license"]
  pruneopts = "UT"
  revision = "2cfd321de3ee5d5f8a5fda2521d1703478334d98"

[[projects]]
  digest = "1:98a70115729234dc73ee7bb83973cb39cb8fedf278d17df77264382bad0183ec"
  name = "go.uber.org/zap"
  packages = [
    ".",
    "buffer",
    "internal/bufferpool",
    "internal/color",
    "internal/exit",
    "zapcore",
    "zaptest/observer",
  ]
  pruneopts = "UT"
  revision = "a6015e13fab9b744d96085308ce4e8f11bad1996"
  version = "v1.12.0"

[[projects]]
  branch = "master"
  digest = "1:21d7bad9b7da270fd2d50aba8971a041bd691165c95096a2a4c68db823cbc86a"
  name = "golang.org/x/lint"
  packages = [
    ".",
    "golint",
  ]
  pruneopts = "UT"
  revision = "16217165b5de779cb6a5e4fc81fa9c1166fda457"

[[projects]]
  branch = "master"
  digest = "1:f8b491a7c25030a895a0e579742d07136e6958e77ef2d46e769db8eec4e58fcd"
  name = "golang.org/x/net"
  packages = [
    "context",
    "context/ctxhttp",
  ]
  pruneopts = "UT"
  revision = "0deb6923b6d97481cb43bc1043fe5b72a0143032"

[[projects]]
  branch = "master"
  digest = "1:5dfb17d45415b7b8927382f53955a66f55f9d9d11557aa82f7f481d642ab247a"
  name = "golang.org/x/sys"
  packages = ["windows"]
  pruneopts = "UT"
  revision = "f43be2a4598cf3a47be9f94f0c28197ed9eae611"

[[projects]]
  branch = "master"
  digest = "1:bae8b3bf837d9d7f601776f37f44e031d46943677beff8fb2eb9c7317d44de2f"
  name = "golang.org/x/tools"
  packages = [
    "go/analysis",
    "go/analysis/passes/inspect",
    "go/ast/astutil",
    "go/ast/inspector",
    "go/buildutil",
    "go/gcexportdata",
    "go/internal/gcimporter",
    "go/internal/packagesdriver",
    "go/packages",
    "go/types/objectpath",
    "go/types/typeutil",
    "internal/fastwalk",
    "internal/gopathwalk",
    "internal/semver",
    "internal/span",
  ]
  pruneopts = "UT"
  revision = "8dbcdeb83d3faec5315146800b375c4962a42fc6"

[[projects]]
  digest = "1:59f10c1537d2199d9115d946927fe31165959a95190849c82ff11e05803528b0"
  name = "gopkg.in/yaml.v2"
  packages = ["."]
  pruneopts = "UT"
  revision = "f221b8435cfb71e54062f6c6e99e9ade30b124d5"
  version = "v2.2.4"

[[projects]]
  digest = "1:131158a88aad1f94854d0aa21a64af2802d0a470fb0f01cb33c04fafd2047111"
  name = "honnef.co/go/tools"
  packages = [
    "arg",
    "cmd/staticcheck",
    "config",
    "deprecated",
    "facts",
    "functions",
    "go/types/typeutil",
    "internal/cache",
    "internal/passes/buildssa",
    "internal/renameio",
    "internal/sharedcheck",
    "lint",
    "lint/lintdsl",
    "lint/lintutil",
    "lint/lintutil/format",
    "loader",
    "printf",
    "simple",
    "ssa",
    "ssautil",
    "staticcheck",
    "staticcheck/vrp",
    "stylecheck",
    "unused",
    "version",
  ]
  pruneopts = "UT"
  revision = "afd67930eec2a9ed3e9b19f684d17a062285f16a"
  version = "2019.2.3"

[solve-meta]
  analyzer-name = "dep"
  analyzer-version = 1
  input-imports = [
    "github.com/crossdock/crossdock-go",
    "github.com/golang/mock/gomock",
    "github.com/opentracing/opentracing-go",
    "github.com/opentracing/opentracing-go/ext",
    "github.com/opentracing/opentracing-go/harness",
    "github.com/opentracing/opentracing-go/log",
    "github.com/pkg/errors",
    "github.com/prometheus/client_golang/prometheus",
    "github.com/stretchr/testify/assert",
    "github.com/stretchr/testify/mock",
    "github.com/stretchr/testify/require",
    "github.com/stretchr/testify/suite",
    "github.com/uber/jaeger-client-go",
    "github.com/uber/jaeger-client-go/config",
    "github.com/uber/jaeger-client-go/crossdock/client",
    "github.com/uber/jaeger-client-go/crossdock/common",
    "github.com/uber/jaeger-client-go/crossdock/endtoend",
    "github.com/uber/jaeger-client-go/crossdock/log",
    "github.com/uber/jaeger-client-go/crossdock/server",
    "github.com/uber/jaeger-client-go/crossdock/thrift/tracetest",
    "github.com/uber/jaeger-client-go/internal/baggage",
    "github.com/uber/jaeger-client-go/internal/baggage/remote",
    "github.com/uber/jaeger-client-go/internal/reporterstats",
    "github.com/uber/jaeger-client-go/internal/spanlog",
    "github.com/uber/jaeger-client-go/internal/throttler",
    "github.com/uber/jaeger-client-go/internal/throttler/remote",
    "github.com/uber/jaeger-client-go/log",
    "github.com/uber/jaeger-client-go/log/zap/mock_opentracing",
    "github.com/uber/jaeger-client-go/rpcmetrics",
    "github.com/uber/jaeger-client-go/testutils",
    "github.com/uber/jaeger-client-go/thrift",
    "github.com/uber/jaeger-client-go/thrift-gen/agent",
    "github.com/uber/jaeger-client-go/thrift-gen/baggage",
    "github.com/uber/jaeger-client-go/thrift-gen/jaeger",
    "github.com/uber/jaeger-client-go/thrift-gen/sampling",
    "github.com/uber/jaeger-client-go/thrift-gen/zipkincore",
    "github.com/uber/jaeger-client-go/transport",
    "github.com/uber/jaeger-client-go/transport/zipkin",
    "github.com/uber/jaeger-client-go/utils",
    "github.com/uber/jaeger-lib/metrics",
    "github.com/uber/jaeger-lib/metrics/metricstest",
    "github.com/uber/jaeger-lib/metrics/prometheus",
    "go.uber.org/atomic",
    "go.uber.org/zap",
    "go.uber.org/zap/zapcore",
    "go.uber.org/zap/zaptest/observer",
  ]
  solver-name = "gps-cdcl"
  solver-version = 1
