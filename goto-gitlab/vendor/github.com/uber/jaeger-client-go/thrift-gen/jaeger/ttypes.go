// Autogenerated by Thrift Compiler (0.9.3)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package jaeger

import (
	"bytes"
	"fmt"
	"github.com/uber/jaeger-client-go/thrift"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = bytes.Equal

var GoUnusedProtection__ int

type TagType int64

const (
	TagType_STRING TagType = 0
	TagType_DOUBLE TagType = 1
	TagType_BOOL   TagType = 2
	TagType_LONG   TagType = 3
	TagType_BINARY TagType = 4
)

func (p TagType) String() string {
	switch p {
	case TagType_STRING:
		return "STRING"
	case TagType_DOUBLE:
		return "DOUBLE"
	case TagType_BOOL:
		return "BOOL"
	case TagType_LONG:
		return "LONG"
	case TagType_BINARY:
		return "BINARY"
	}
	return "<UNSET>"
}

func TagTypeFromString(s string) (TagType, error) {
	switch s {
	case "STRING":
		return TagType_STRING, nil
	case "DOUBLE":
		return TagType_DOUBLE, nil
	case "BOOL":
		return TagType_BOOL, nil
	case "LONG":
		return TagType_LONG, nil
	case "BINARY":
		return TagType_BINARY, nil
	}
	return TagType(0), fmt.Errorf("not a valid TagType string")
}

func TagTypePtr(v TagType) *TagType { return &v }

func (p TagType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *TagType) UnmarshalText(text []byte) error {
	q, err := TagTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type SpanRefType int64

const (
	SpanRefType_CHILD_OF     SpanRefType = 0
	SpanRefType_FOLLOWS_FROM SpanRefType = 1
)

func (p SpanRefType) String() string {
	switch p {
	case SpanRefType_CHILD_OF:
		return "CHILD_OF"
	case SpanRefType_FOLLOWS_FROM:
		return "FOLLOWS_FROM"
	}
	return "<UNSET>"
}

func SpanRefTypeFromString(s string) (SpanRefType, error) {
	switch s {
	case "CHILD_OF":
		return SpanRefType_CHILD_OF, nil
	case "FOLLOWS_FROM":
		return SpanRefType_FOLLOWS_FROM, nil
	}
	return SpanRefType(0), fmt.Errorf("not a valid SpanRefType string")
}

func SpanRefTypePtr(v SpanRefType) *SpanRefType { return &v }

func (p SpanRefType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SpanRefType) UnmarshalText(text []byte) error {
	q, err := SpanRefTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

// Attributes:
//  - Key
//  - VType
//  - VStr
//  - VDouble
//  - VBool
//  - VLong
//  - VBinary
type Tag struct {
	Key     string   `thrift:"key,1,required" json:"key"`
	VType   TagType  `thrift:"vType,2,required" json:"vType"`
	VStr    *string  `thrift:"vStr,3" json:"vStr,omitempty"`
	VDouble *float64 `thrift:"vDouble,4" json:"vDouble,omitempty"`
	VBool   *bool    `thrift:"vBool,5" json:"vBool,omitempty"`
	VLong   *int64   `thrift:"vLong,6" json:"vLong,omitempty"`
	VBinary []byte   `thrift:"vBinary,7" json:"vBinary,omitempty"`
}

func NewTag() *Tag {
	return &Tag{}
}

func (p *Tag) GetKey() string {
	return p.Key
}

func (p *Tag) GetVType() TagType {
	return p.VType
}

var Tag_VStr_DEFAULT string

func (p *Tag) GetVStr() string {
	if !p.IsSetVStr() {
		return Tag_VStr_DEFAULT
	}
	return *p.VStr
}

var Tag_VDouble_DEFAULT float64

func (p *Tag) GetVDouble() float64 {
	if !p.IsSetVDouble() {
		return Tag_VDouble_DEFAULT
	}
	return *p.VDouble
}

var Tag_VBool_DEFAULT bool

func (p *Tag) GetVBool() bool {
	if !p.IsSetVBool() {
		return Tag_VBool_DEFAULT
	}
	return *p.VBool
}

var Tag_VLong_DEFAULT int64

func (p *Tag) GetVLong() int64 {
	if !p.IsSetVLong() {
		return Tag_VLong_DEFAULT
	}
	return *p.VLong
}

var Tag_VBinary_DEFAULT []byte

func (p *Tag) GetVBinary() []byte {
	return p.VBinary
}
func (p *Tag) IsSetVStr() bool {
	return p.VStr != nil
}

func (p *Tag) IsSetVDouble() bool {
	return p.VDouble != nil
}

func (p *Tag) IsSetVBool() bool {
	return p.VBool != nil
}

func (p *Tag) IsSetVLong() bool {
	return p.VLong != nil
}

func (p *Tag) IsSetVBinary() bool {
	return p.VBinary != nil
}

func (p *Tag) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKey bool = false
	var issetVType bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if err := p.readField1(iprot); err != nil {
				return err
			}
			issetKey = true
		case 2:
			if err := p.readField2(iprot); err != nil {
				return err
			}
			issetVType = true
		case 3:
			if err := p.readField3(iprot); err != nil {
				return err
			}
		case 4:
			if err := p.readField4(iprot); err != nil {
				return err
			}
		case 5:
			if err := p.readField5(iprot); err != nil {
				return err
			}
		case 6:
			if err := p.readField6(iprot); err != nil {
				return err
			}
		case 7:
			if err := p.readField7(iprot); err != nil {
				return err
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKey {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Key is not set"))
	}
	if !issetVType {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field VType is not set"))
	}
	return nil
}

func (p *Tag) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Key = v
	}
	return nil
}

func (p *Tag) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		temp := TagType(v)
		p.VType = temp
	}
	return nil
}

func (p *Tag) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.VStr = &v
	}
	return nil
}

func (p *Tag) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.VDouble = &v
	}
	return nil
}

func (p *Tag) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return thrift.PrependError("error reading field 5: ", err)
	} else {
		p.VBool = &v
	}
	return nil
}

func (p *Tag) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return thrift.PrependError("error reading field 6: ", err)
	} else {
		p.VLong = &v
	}
	return nil
}

func (p *Tag) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(); err != nil {
		return thrift.PrependError("error reading field 7: ", err)
	} else {
		p.VBinary = v
	}
	return nil
}

func (p *Tag) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Tag"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *Tag) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("key", thrift.STRING, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:key: ", p), err)
	}
	if err := oprot.WriteString(string(p.Key)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.key (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:key: ", p), err)
	}
	return err
}

func (p *Tag) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("vType", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:vType: ", p), err)
	}
	if err := oprot.WriteI32(int32(p.VType)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.vType (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:vType: ", p), err)
	}
	return err
}

func (p *Tag) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetVStr() {
		if err := oprot.WriteFieldBegin("vStr", thrift.STRING, 3); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:vStr: ", p), err)
		}
		if err := oprot.WriteString(string(*p.VStr)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.vStr (3) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 3:vStr: ", p), err)
		}
	}
	return err
}

func (p *Tag) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetVDouble() {
		if err := oprot.WriteFieldBegin("vDouble", thrift.DOUBLE, 4); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:vDouble: ", p), err)
		}
		if err := oprot.WriteDouble(float64(*p.VDouble)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.vDouble (4) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 4:vDouble: ", p), err)
		}
	}
	return err
}

func (p *Tag) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetVBool() {
		if err := oprot.WriteFieldBegin("vBool", thrift.BOOL, 5); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:vBool: ", p), err)
		}
		if err := oprot.WriteBool(bool(*p.VBool)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.vBool (5) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 5:vBool: ", p), err)
		}
	}
	return err
}

func (p *Tag) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetVLong() {
		if err := oprot.WriteFieldBegin("vLong", thrift.I64, 6); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:vLong: ", p), err)
		}
		if err := oprot.WriteI64(int64(*p.VLong)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.vLong (6) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 6:vLong: ", p), err)
		}
	}
	return err
}

func (p *Tag) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetVBinary() {
		if err := oprot.WriteFieldBegin("vBinary", thrift.STRING, 7); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:vBinary: ", p), err)
		}
		if err := oprot.WriteBinary(p.VBinary); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.vBinary (7) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 7:vBinary: ", p), err)
		}
	}
	return err
}

func (p *Tag) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Tag(%+v)", *p)
}

// Attributes:
//  - Timestamp
//  - Fields
type Log struct {
	Timestamp int64  `thrift:"timestamp,1,required" json:"timestamp"`
	Fields    []*Tag `thrift:"fields,2,required" json:"fields"`
}

func NewLog() *Log {
	return &Log{}
}

func (p *Log) GetTimestamp() int64 {
	return p.Timestamp
}

func (p *Log) GetFields() []*Tag {
	return p.Fields
}
func (p *Log) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetTimestamp bool = false
	var issetFields bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if err := p.readField1(iprot); err != nil {
				return err
			}
			issetTimestamp = true
		case 2:
			if err := p.readField2(iprot); err != nil {
				return err
			}
			issetFields = true
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetTimestamp {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Timestamp is not set"))
	}
	if !issetFields {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Fields is not set"))
	}
	return nil
}

func (p *Log) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Timestamp = v
	}
	return nil
}

func (p *Log) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]*Tag, 0, size)
	p.Fields = tSlice
	for i := 0; i < size; i++ {
		_elem0 := &Tag{}
		if err := _elem0.Read(iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem0), err)
		}
		p.Fields = append(p.Fields, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *Log) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Log"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *Log) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timestamp", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:timestamp: ", p), err)
	}
	if err := oprot.WriteI64(int64(p.Timestamp)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.timestamp (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:timestamp: ", p), err)
	}
	return err
}

func (p *Log) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fields", thrift.LIST, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:fields: ", p), err)
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Fields)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.Fields {
		if err := v.Write(oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:fields: ", p), err)
	}
	return err
}

func (p *Log) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Log(%+v)", *p)
}

// Attributes:
//  - RefType
//  - TraceIdLow
//  - TraceIdHigh
//  - SpanId
type SpanRef struct {
	RefType     SpanRefType `thrift:"refType,1,required" json:"refType"`
	TraceIdLow  int64       `thrift:"traceIdLow,2,required" json:"traceIdLow"`
	TraceIdHigh int64       `thrift:"traceIdHigh,3,required" json:"traceIdHigh"`
	SpanId      int64       `thrift:"spanId,4,required" json:"spanId"`
}

func NewSpanRef() *SpanRef {
	return &SpanRef{}
}

func (p *SpanRef) GetRefType() SpanRefType {
	return p.RefType
}

func (p *SpanRef) GetTraceIdLow() int64 {
	return p.TraceIdLow
}

func (p *SpanRef) GetTraceIdHigh() int64 {
	return p.TraceIdHigh
}

func (p *SpanRef) GetSpanId() int64 {
	return p.SpanId
}
func (p *SpanRef) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetRefType bool = false
	var issetTraceIdLow bool = false
	var issetTraceIdHigh bool = false
	var issetSpanId bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if err := p.readField1(iprot); err != nil {
				return err
			}
			issetRefType = true
		case 2:
			if err := p.readField2(iprot); err != nil {
				return err
			}
			issetTraceIdLow = true
		case 3:
			if err := p.readField3(iprot); err != nil {
				return err
			}
			issetTraceIdHigh = true
		case 4:
			if err := p.readField4(iprot); err != nil {
				return err
			}
			issetSpanId = true
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetRefType {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field RefType is not set"))
	}
	if !issetTraceIdLow {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field TraceIdLow is not set"))
	}
	if !issetTraceIdHigh {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field TraceIdHigh is not set"))
	}
	if !issetSpanId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field SpanId is not set"))
	}
	return nil
}

func (p *SpanRef) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		temp := SpanRefType(v)
		p.RefType = temp
	}
	return nil
}

func (p *SpanRef) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.TraceIdLow = v
	}
	return nil
}

func (p *SpanRef) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.TraceIdHigh = v
	}
	return nil
}

func (p *SpanRef) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.SpanId = v
	}
	return nil
}

func (p *SpanRef) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SpanRef"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *SpanRef) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("refType", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:refType: ", p), err)
	}
	if err := oprot.WriteI32(int32(p.RefType)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.refType (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:refType: ", p), err)
	}
	return err
}

func (p *SpanRef) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("traceIdLow", thrift.I64, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:traceIdLow: ", p), err)
	}
	if err := oprot.WriteI64(int64(p.TraceIdLow)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.traceIdLow (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:traceIdLow: ", p), err)
	}
	return err
}

func (p *SpanRef) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("traceIdHigh", thrift.I64, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:traceIdHigh: ", p), err)
	}
	if err := oprot.WriteI64(int64(p.TraceIdHigh)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.traceIdHigh (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:traceIdHigh: ", p), err)
	}
	return err
}

func (p *SpanRef) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spanId", thrift.I64, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:spanId: ", p), err)
	}
	if err := oprot.WriteI64(int64(p.SpanId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.spanId (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:spanId: ", p), err)
	}
	return err
}

func (p *SpanRef) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SpanRef(%+v)", *p)
}

// Attributes:
//  - TraceIdLow
//  - TraceIdHigh
//  - SpanId
//  - ParentSpanId
//  - OperationName
//  - References
//  - Flags
//  - StartTime
//  - Duration
//  - Tags
//  - Logs
type Span struct {
	TraceIdLow    int64      `thrift:"traceIdLow,1,required" json:"traceIdLow"`
	TraceIdHigh   int64      `thrift:"traceIdHigh,2,required" json:"traceIdHigh"`
	SpanId        int64      `thrift:"spanId,3,required" json:"spanId"`
	ParentSpanId  int64      `thrift:"parentSpanId,4,required" json:"parentSpanId"`
	OperationName string     `thrift:"operationName,5,required" json:"operationName"`
	References    []*SpanRef `thrift:"references,6" json:"references,omitempty"`
	Flags         int32      `thrift:"flags,7,required" json:"flags"`
	StartTime     int64      `thrift:"startTime,8,required" json:"startTime"`
	Duration      int64      `thrift:"duration,9,required" json:"duration"`
	Tags          []*Tag     `thrift:"tags,10" json:"tags,omitempty"`
	Logs          []*Log     `thrift:"logs,11" json:"logs,omitempty"`
}

func NewSpan() *Span {
	return &Span{}
}

func (p *Span) GetTraceIdLow() int64 {
	return p.TraceIdLow
}

func (p *Span) GetTraceIdHigh() int64 {
	return p.TraceIdHigh
}

func (p *Span) GetSpanId() int64 {
	return p.SpanId
}

func (p *Span) GetParentSpanId() int64 {
	return p.ParentSpanId
}

func (p *Span) GetOperationName() string {
	return p.OperationName
}

var Span_References_DEFAULT []*SpanRef

func (p *Span) GetReferences() []*SpanRef {
	return p.References
}

func (p *Span) GetFlags() int32 {
	return p.Flags
}

func (p *Span) GetStartTime() int64 {
	return p.StartTime
}

func (p *Span) GetDuration() int64 {
	return p.Duration
}

var Span_Tags_DEFAULT []*Tag

func (p *Span) GetTags() []*Tag {
	return p.Tags
}

var Span_Logs_DEFAULT []*Log

func (p *Span) GetLogs() []*Log {
	return p.Logs
}
func (p *Span) IsSetReferences() bool {
	return p.References != nil
}

func (p *Span) IsSetTags() bool {
	return p.Tags != nil
}

func (p *Span) IsSetLogs() bool {
	return p.Logs != nil
}

func (p *Span) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetTraceIdLow bool = false
	var issetTraceIdHigh bool = false
	var issetSpanId bool = false
	var issetParentSpanId bool = false
	var issetOperationName bool = false
	var issetFlags bool = false
	var issetStartTime bool = false
	var issetDuration bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if err := p.readField1(iprot); err != nil {
				return err
			}
			issetTraceIdLow = true
		case 2:
			if err := p.readField2(iprot); err != nil {
				return err
			}
			issetTraceIdHigh = true
		case 3:
			if err := p.readField3(iprot); err != nil {
				return err
			}
			issetSpanId = true
		case 4:
			if err := p.readField4(iprot); err != nil {
				return err
			}
			issetParentSpanId = true
		case 5:
			if err := p.readField5(iprot); err != nil {
				return err
			}
			issetOperationName = true
		case 6:
			if err := p.readField6(iprot); err != nil {
				return err
			}
		case 7:
			if err := p.readField7(iprot); err != nil {
				return err
			}
			issetFlags = true
		case 8:
			if err := p.readField8(iprot); err != nil {
				return err
			}
			issetStartTime = true
		case 9:
			if err := p.readField9(iprot); err != nil {
				return err
			}
			issetDuration = true
		case 10:
			if err := p.readField10(iprot); err != nil {
				return err
			}
		case 11:
			if err := p.readField11(iprot); err != nil {
				return err
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetTraceIdLow {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field TraceIdLow is not set"))
	}
	if !issetTraceIdHigh {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field TraceIdHigh is not set"))
	}
	if !issetSpanId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field SpanId is not set"))
	}
	if !issetParentSpanId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ParentSpanId is not set"))
	}
	if !issetOperationName {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field OperationName is not set"))
	}
	if !issetFlags {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Flags is not set"))
	}
	if !issetStartTime {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field StartTime is not set"))
	}
	if !issetDuration {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Duration is not set"))
	}
	return nil
}

func (p *Span) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.TraceIdLow = v
	}
	return nil
}

func (p *Span) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.TraceIdHigh = v
	}
	return nil
}

func (p *Span) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.SpanId = v
	}
	return nil
}

func (p *Span) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.ParentSpanId = v
	}
	return nil
}

func (p *Span) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return thrift.PrependError("error reading field 5: ", err)
	} else {
		p.OperationName = v
	}
	return nil
}

func (p *Span) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]*SpanRef, 0, size)
	p.References = tSlice
	for i := 0; i < size; i++ {
		_elem1 := &SpanRef{}
		if err := _elem1.Read(iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem1), err)
		}
		p.References = append(p.References, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *Span) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return thrift.PrependError("error reading field 7: ", err)
	} else {
		p.Flags = v
	}
	return nil
}

func (p *Span) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return thrift.PrependError("error reading field 8: ", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *Span) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return thrift.PrependError("error reading field 9: ", err)
	} else {
		p.Duration = v
	}
	return nil
}

func (p *Span) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]*Tag, 0, size)
	p.Tags = tSlice
	for i := 0; i < size; i++ {
		_elem2 := &Tag{}
		if err := _elem2.Read(iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem2), err)
		}
		p.Tags = append(p.Tags, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *Span) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]*Log, 0, size)
	p.Logs = tSlice
	for i := 0; i < size; i++ {
		_elem3 := &Log{}
		if err := _elem3.Read(iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem3), err)
		}
		p.Logs = append(p.Logs, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *Span) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Span"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *Span) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("traceIdLow", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:traceIdLow: ", p), err)
	}
	if err := oprot.WriteI64(int64(p.TraceIdLow)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.traceIdLow (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:traceIdLow: ", p), err)
	}
	return err
}

func (p *Span) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("traceIdHigh", thrift.I64, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:traceIdHigh: ", p), err)
	}
	if err := oprot.WriteI64(int64(p.TraceIdHigh)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.traceIdHigh (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:traceIdHigh: ", p), err)
	}
	return err
}

func (p *Span) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spanId", thrift.I64, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:spanId: ", p), err)
	}
	if err := oprot.WriteI64(int64(p.SpanId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.spanId (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:spanId: ", p), err)
	}
	return err
}

func (p *Span) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("parentSpanId", thrift.I64, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:parentSpanId: ", p), err)
	}
	if err := oprot.WriteI64(int64(p.ParentSpanId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.parentSpanId (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:parentSpanId: ", p), err)
	}
	return err
}

func (p *Span) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("operationName", thrift.STRING, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:operationName: ", p), err)
	}
	if err := oprot.WriteString(string(p.OperationName)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.operationName (5) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:operationName: ", p), err)
	}
	return err
}

func (p *Span) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetReferences() {
		if err := oprot.WriteFieldBegin("references", thrift.LIST, 6); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:references: ", p), err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.References)); err != nil {
			return thrift.PrependError("error writing list begin: ", err)
		}
		for _, v := range p.References {
			if err := v.Write(oprot); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return thrift.PrependError("error writing list end: ", err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 6:references: ", p), err)
		}
	}
	return err
}

func (p *Span) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("flags", thrift.I32, 7); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:flags: ", p), err)
	}
	if err := oprot.WriteI32(int32(p.Flags)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.flags (7) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 7:flags: ", p), err)
	}
	return err
}

func (p *Span) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 8); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:startTime: ", p), err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.startTime (8) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 8:startTime: ", p), err)
	}
	return err
}

func (p *Span) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duration", thrift.I64, 9); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:duration: ", p), err)
	}
	if err := oprot.WriteI64(int64(p.Duration)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.duration (9) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 9:duration: ", p), err)
	}
	return err
}

func (p *Span) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetTags() {
		if err := oprot.WriteFieldBegin("tags", thrift.LIST, 10); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:tags: ", p), err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tags)); err != nil {
			return thrift.PrependError("error writing list begin: ", err)
		}
		for _, v := range p.Tags {
			if err := v.Write(oprot); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return thrift.PrependError("error writing list end: ", err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 10:tags: ", p), err)
		}
	}
	return err
}

func (p *Span) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetLogs() {
		if err := oprot.WriteFieldBegin("logs", thrift.LIST, 11); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:logs: ", p), err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Logs)); err != nil {
			return thrift.PrependError("error writing list begin: ", err)
		}
		for _, v := range p.Logs {
			if err := v.Write(oprot); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return thrift.PrependError("error writing list end: ", err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 11:logs: ", p), err)
		}
	}
	return err
}

func (p *Span) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Span(%+v)", *p)
}

// Attributes:
//  - ServiceName
//  - Tags
type Process struct {
	ServiceName string `thrift:"serviceName,1,required" json:"serviceName"`
	Tags        []*Tag `thrift:"tags,2" json:"tags,omitempty"`
}

func NewProcess() *Process {
	return &Process{}
}

func (p *Process) GetServiceName() string {
	return p.ServiceName
}

var Process_Tags_DEFAULT []*Tag

func (p *Process) GetTags() []*Tag {
	return p.Tags
}
func (p *Process) IsSetTags() bool {
	return p.Tags != nil
}

func (p *Process) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetServiceName bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if err := p.readField1(iprot); err != nil {
				return err
			}
			issetServiceName = true
		case 2:
			if err := p.readField2(iprot); err != nil {
				return err
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetServiceName {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ServiceName is not set"))
	}
	return nil
}

func (p *Process) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.ServiceName = v
	}
	return nil
}

func (p *Process) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]*Tag, 0, size)
	p.Tags = tSlice
	for i := 0; i < size; i++ {
		_elem4 := &Tag{}
		if err := _elem4.Read(iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem4), err)
		}
		p.Tags = append(p.Tags, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *Process) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Process"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *Process) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("serviceName", thrift.STRING, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:serviceName: ", p), err)
	}
	if err := oprot.WriteString(string(p.ServiceName)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.serviceName (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:serviceName: ", p), err)
	}
	return err
}

func (p *Process) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTags() {
		if err := oprot.WriteFieldBegin("tags", thrift.LIST, 2); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:tags: ", p), err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tags)); err != nil {
			return thrift.PrependError("error writing list begin: ", err)
		}
		for _, v := range p.Tags {
			if err := v.Write(oprot); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return thrift.PrependError("error writing list end: ", err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 2:tags: ", p), err)
		}
	}
	return err
}

func (p *Process) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Process(%+v)", *p)
}

// Attributes:
//  - FullQueueDroppedSpans
//  - TooLargeDroppedSpans
//  - FailedToEmitSpans
type ClientStats struct {
	FullQueueDroppedSpans int64 `thrift:"fullQueueDroppedSpans,1,required" json:"fullQueueDroppedSpans"`
	TooLargeDroppedSpans  int64 `thrift:"tooLargeDroppedSpans,2,required" json:"tooLargeDroppedSpans"`
	FailedToEmitSpans     int64 `thrift:"failedToEmitSpans,3,required" json:"failedToEmitSpans"`
}

func NewClientStats() *ClientStats {
	return &ClientStats{}
}

func (p *ClientStats) GetFullQueueDroppedSpans() int64 {
	return p.FullQueueDroppedSpans
}

func (p *ClientStats) GetTooLargeDroppedSpans() int64 {
	return p.TooLargeDroppedSpans
}

func (p *ClientStats) GetFailedToEmitSpans() int64 {
	return p.FailedToEmitSpans
}
func (p *ClientStats) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetFullQueueDroppedSpans bool = false
	var issetTooLargeDroppedSpans bool = false
	var issetFailedToEmitSpans bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if err := p.readField1(iprot); err != nil {
				return err
			}
			issetFullQueueDroppedSpans = true
		case 2:
			if err := p.readField2(iprot); err != nil {
				return err
			}
			issetTooLargeDroppedSpans = true
		case 3:
			if err := p.readField3(iprot); err != nil {
				return err
			}
			issetFailedToEmitSpans = true
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetFullQueueDroppedSpans {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field FullQueueDroppedSpans is not set"))
	}
	if !issetTooLargeDroppedSpans {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field TooLargeDroppedSpans is not set"))
	}
	if !issetFailedToEmitSpans {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field FailedToEmitSpans is not set"))
	}
	return nil
}

func (p *ClientStats) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.FullQueueDroppedSpans = v
	}
	return nil
}

func (p *ClientStats) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.TooLargeDroppedSpans = v
	}
	return nil
}

func (p *ClientStats) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.FailedToEmitSpans = v
	}
	return nil
}

func (p *ClientStats) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ClientStats"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *ClientStats) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fullQueueDroppedSpans", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:fullQueueDroppedSpans: ", p), err)
	}
	if err := oprot.WriteI64(int64(p.FullQueueDroppedSpans)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.fullQueueDroppedSpans (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:fullQueueDroppedSpans: ", p), err)
	}
	return err
}

func (p *ClientStats) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tooLargeDroppedSpans", thrift.I64, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:tooLargeDroppedSpans: ", p), err)
	}
	if err := oprot.WriteI64(int64(p.TooLargeDroppedSpans)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.tooLargeDroppedSpans (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:tooLargeDroppedSpans: ", p), err)
	}
	return err
}

func (p *ClientStats) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("failedToEmitSpans", thrift.I64, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:failedToEmitSpans: ", p), err)
	}
	if err := oprot.WriteI64(int64(p.FailedToEmitSpans)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.failedToEmitSpans (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:failedToEmitSpans: ", p), err)
	}
	return err
}

func (p *ClientStats) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ClientStats(%+v)", *p)
}

// Attributes:
//  - Process
//  - Spans
//  - SeqNo
//  - Stats
type Batch struct {
	Process *Process     `thrift:"process,1,required" json:"process"`
	Spans   []*Span      `thrift:"spans,2,required" json:"spans"`
	SeqNo   *int64       `thrift:"seqNo,3" json:"seqNo,omitempty"`
	Stats   *ClientStats `thrift:"stats,4" json:"stats,omitempty"`
}

func NewBatch() *Batch {
	return &Batch{}
}

var Batch_Process_DEFAULT *Process

func (p *Batch) GetProcess() *Process {
	if !p.IsSetProcess() {
		return Batch_Process_DEFAULT
	}
	return p.Process
}

func (p *Batch) GetSpans() []*Span {
	return p.Spans
}

var Batch_SeqNo_DEFAULT int64

func (p *Batch) GetSeqNo() int64 {
	if !p.IsSetSeqNo() {
		return Batch_SeqNo_DEFAULT
	}
	return *p.SeqNo
}

var Batch_Stats_DEFAULT *ClientStats

func (p *Batch) GetStats() *ClientStats {
	if !p.IsSetStats() {
		return Batch_Stats_DEFAULT
	}
	return p.Stats
}
func (p *Batch) IsSetProcess() bool {
	return p.Process != nil
}

func (p *Batch) IsSetSeqNo() bool {
	return p.SeqNo != nil
}

func (p *Batch) IsSetStats() bool {
	return p.Stats != nil
}

func (p *Batch) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetProcess bool = false
	var issetSpans bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if err := p.readField1(iprot); err != nil {
				return err
			}
			issetProcess = true
		case 2:
			if err := p.readField2(iprot); err != nil {
				return err
			}
			issetSpans = true
		case 3:
			if err := p.readField3(iprot); err != nil {
				return err
			}
		case 4:
			if err := p.readField4(iprot); err != nil {
				return err
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetProcess {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Process is not set"))
	}
	if !issetSpans {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Spans is not set"))
	}
	return nil
}

func (p *Batch) readField1(iprot thrift.TProtocol) error {
	p.Process = &Process{}
	if err := p.Process.Read(iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Process), err)
	}
	return nil
}

func (p *Batch) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]*Span, 0, size)
	p.Spans = tSlice
	for i := 0; i < size; i++ {
		_elem5 := &Span{}
		if err := _elem5.Read(iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem5), err)
		}
		p.Spans = append(p.Spans, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *Batch) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.SeqNo = &v
	}
	return nil
}

func (p *Batch) readField4(iprot thrift.TProtocol) error {
	p.Stats = &ClientStats{}
	if err := p.Stats.Read(iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Stats), err)
	}
	return nil
}

func (p *Batch) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Batch"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *Batch) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("process", thrift.STRUCT, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:process: ", p), err)
	}
	if err := p.Process.Write(oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Process), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:process: ", p), err)
	}
	return err
}

func (p *Batch) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spans", thrift.LIST, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:spans: ", p), err)
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Spans)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.Spans {
		if err := v.Write(oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:spans: ", p), err)
	}
	return err
}

func (p *Batch) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSeqNo() {
		if err := oprot.WriteFieldBegin("seqNo", thrift.I64, 3); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:seqNo: ", p), err)
		}
		if err := oprot.WriteI64(int64(*p.SeqNo)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.seqNo (3) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 3:seqNo: ", p), err)
		}
	}
	return err
}

func (p *Batch) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetStats() {
		if err := oprot.WriteFieldBegin("stats", thrift.STRUCT, 4); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:stats: ", p), err)
		}
		if err := p.Stats.Write(oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Stats), err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 4:stats: ", p), err)
		}
	}
	return err
}

func (p *Batch) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Batch(%+v)", *p)
}

// Attributes:
//  - Ok
type BatchSubmitResponse struct {
	Ok bool `thrift:"ok,1,required" json:"ok"`
}

func NewBatchSubmitResponse() *BatchSubmitResponse {
	return &BatchSubmitResponse{}
}

func (p *BatchSubmitResponse) GetOk() bool {
	return p.Ok
}
func (p *BatchSubmitResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetOk bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if err := p.readField1(iprot); err != nil {
				return err
			}
			issetOk = true
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetOk {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ok is not set"))
	}
	return nil
}

func (p *BatchSubmitResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Ok = v
	}
	return nil
}

func (p *BatchSubmitResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("BatchSubmitResponse"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *BatchSubmitResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ok", thrift.BOOL, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ok: ", p), err)
	}
	if err := oprot.WriteBool(bool(p.Ok)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.ok (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ok: ", p), err)
	}
	return err
}

func (p *BatchSubmitResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchSubmitResponse(%+v)", *p)
}
