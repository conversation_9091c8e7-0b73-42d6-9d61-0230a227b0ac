This project was automatically exported from code.google.com/p/go-uuid

# uuid ![build status](https://travis-ci.org/pborman/uuid.svg?branch=master)
The uuid package generates and inspects UUIDs based on [RFC 4122](http://tools.ietf.org/html/rfc4122) and DCE 1.1: Authentication and Security Services. 

This package now leverages the github.com/google/uuid package (which is based off an earlier version of this package).

###### Install
`go get github.com/pborman/uuid`

###### Documentation 
[![GoDoc](https://godoc.org/github.com/pborman/uuid?status.svg)](http://godoc.org/github.com/pborman/uuid)

Full `go doc` style documentation for the package can be viewed online without installing this package by using the GoDoc site here: 
http://godoc.org/github.com/pborman/uuid
