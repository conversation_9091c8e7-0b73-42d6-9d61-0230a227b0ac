# Security Policy

If you have discovered a security vulnerability in this project, please report it
privately. **Do not disclose it as a public issue.** This gives us time to work with you
to fix the issue before public exposure, reducing the chance that the exploit will be
used before a patch is released.

You may submit the report in the following ways:

- send an <NAME_EMAIL>
- send us a [private vulnerability report](https://github.com/go-logr/logr/security/advisories/new)

Please provide the following information in your report:

- A description of the vulnerability and its impact
- How to reproduce the issue

We ask that you give us 90 days to work on a fix before public exposure.
