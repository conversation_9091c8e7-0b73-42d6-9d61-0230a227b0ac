load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(default_visibility = ["//visibility:public"])

go_library(
    name = "runtime",
    srcs = [
        "context.go",
        "convert.go",
        "doc.go",
        "errors.go",
        "fieldmask.go",
        "handler.go",
        "marshal_httpbodyproto.go",
        "marshal_json.go",
        "marshal_jsonpb.go",
        "marshal_proto.go",
        "marshaler.go",
        "marshaler_registry.go",
        "mux.go",
        "pattern.go",
        "proto2_convert.go",
        "query.go",
    ],
    importpath = "github.com/grpc-ecosystem/grpc-gateway/v2/runtime",
    deps = [
        "//internal/httprule",
        "//utilities",
        "@org_golang_google_genproto_googleapis_api//httpbody",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//grpclog",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//reflect/protoreflect",
        "@org_golang_google_protobuf//reflect/protoregistry",
        "@org_golang_google_protobuf//types/known/durationpb",
        "@org_golang_google_protobuf//types/known/fieldmaskpb",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_golang_google_protobuf//types/known/wrapperspb",
    ],
)

go_test(
    name = "runtime_test",
    size = "small",
    srcs = [
        "context_test.go",
        "convert_test.go",
        "errors_test.go",
        "fieldmask_test.go",
        "handler_test.go",
        "marshal_httpbodyproto_test.go",
        "marshal_json_test.go",
        "marshal_jsonpb_test.go",
        "marshal_proto_test.go",
        "marshaler_registry_test.go",
        "mux_internal_test.go",
        "mux_test.go",
        "pattern_test.go",
        "query_fuzz_test.go",
        "query_test.go",
    ],
    embed = [":runtime"],
    deps = [
        "//runtime/internal/examplepb",
        "//utilities",
        "@com_github_google_go_cmp//cmp",
        "@com_github_google_go_cmp//cmp/cmpopts",
        "@org_golang_google_genproto_googleapis_api//httpbody",
        "@org_golang_google_genproto_googleapis_rpc//errdetails",
        "@org_golang_google_genproto_googleapis_rpc//status",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//testing/protocmp",
        "@org_golang_google_protobuf//types/known/durationpb",
        "@org_golang_google_protobuf//types/known/emptypb",
        "@org_golang_google_protobuf//types/known/fieldmaskpb",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_golang_google_protobuf//types/known/wrapperspb",
    ],
)

alias(
    name = "go_default_library",
    actual = ":runtime",
    visibility = ["//visibility:public"],
)
