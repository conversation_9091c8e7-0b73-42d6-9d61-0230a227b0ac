# goto-gitlab

goto-gitlab 用于将代码仓库迁移至Gitlab。

迁移范围包括：用户、组、项目、代码、分支、tag、提交、成员、权限。

### 编译命令:

go build -v -mod=vendor -o goto-gitlab

### 使用步骤：

1. 配置.env文件
```env
# 迁移仓库类型，支持的类型: bitbucket
MIGRATE_TYPE=

# Bitbucket配置
MIGRATE_BITBUCKET_URL=
MIGRATE_BITBUCKET_USERNAME=
MIGRATE_BITBUCKET_PASSWORD=

# Gitlab配置
GITLAB_URL=
GITLAB_TOKEN=
```

2. 配置users.txt文件

文件内容示例：
```txt
张三,123456
李四,234567
```

3. 迁移用户命令
```bash
goto-gitlab users
```

4. 迁移项目命令
```bash
goto-gitlab projects
```
