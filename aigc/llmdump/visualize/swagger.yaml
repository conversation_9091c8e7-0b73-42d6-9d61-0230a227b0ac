openapi: 3.0.0
info:
  title: LLMDump Visualize API
  version: v1

paths:
  /api/dir:
    get:
      summary: List directories
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DirRequest'

  /api/dir/{dir}/{request_index}:
    get:
      summary: Get request by index
      parameters:
        - in: path
          name: dir
          required: true
          schema:
            type: string
          description: Directory name
        - in: path
          name: request_index
          required: true
          schema:
            type: integer
          description: Request index
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Request'
components:
  schemas:
    DirRequest:
      type: object
      properties:
        Dir:
          type: string
        RequestIndex:
          type: array
          items:
            type: string

    Request:
      type: object
      properties:
        Input:
          $ref: '#/components/schemas/Input'
        Output:
          $ref: '#/components/schemas/Output'

    Input:
      type: object
      properties:
        Messages:
          type: array
          items:
            $ref: '#/components/schemas/Message'
        Tools:
          type: array
          items:
            $ref: '#/components/schemas/Tool'

    Tool:
      type: object
      properties:
        Name:
          type: string

    Message:
      type: object
      properties:
        Role:
          type: string
        Content:
          type: string

    Output:
      type: object
      properties:
        Messages:
          type: array
          items:
            $ref: '#/components/schemas/Message'
        ToolsCall:
          type: array
          items:
            $ref: '#/components/schemas/ToolCall'

    ToolCall:
      type: object
      properties:
        Name:
          type: string
        Args:
          type: string

    ChatInput:
      type: object
      properties:
        Model:
          type: string
        Stream:
          type: boolean
        Messages:
          type: array
          items:
            type: object
            properties:
              Role:
                type: string
              Content:
                type: string
        ToolChoice:
          type: string
        Tools:
          type: array
          items:
            type: object
            properties:
              Type:
                type: string
              Function:
                type: object
                properties:
                  Name:
                    type: string
                  Description:
                    type: string
                  Parameters:
                    type: object
                    properties:
                      Type:
                        type: string
                      Properties:
                        type: object
                      Required:
                        type: array
                        items:
                          type: string

        User:
          type: string
