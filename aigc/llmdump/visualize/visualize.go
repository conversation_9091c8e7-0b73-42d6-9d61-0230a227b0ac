package visualize

import (
	"bufio"
	"bytes"
	"embed"
	"encoding/json"
	"errors"
	"fmt"
	"io/fs"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"

	"github.com/gorilla/mux"
	"github.com/openai/openai-go"
	"github.com/openai/openai-go/packages/ssestream"
)

//go:embed frontend/build
var distDir embed.FS

type DirRequest struct {
	Dir          string
	RequestIndex []string
}

type Request struct {
	Input  Input
	Output Output
}

type Input struct {
	Messages []Message
	Tools    []Tool
}

type Tool struct {
	Name string
}

type Message struct {
	Role    string
	Content string
}

type Output struct {
	Messages  []Message
	ToolsCall []ToolCall
}

type ToolCall struct {
	Name string
	Args string
}

type ChatInput struct {
	Model    string `json:"model"`
	Stream   bool   `json:"stream"`
	Messages []struct {
		Role    string `json:"role"`
		Content string `json:"content"`
	} `json:"messages"`
	ToolChoice any `json:"tool_choice"`
	Tools      []struct {
		Type     string `json:"type"`
		Function struct {
			Name        string `json:"name"`
			Description string `json:"description"`
			Parameters  struct {
				Type       string   `json:"type"`
				Properties any      `json:"properties"`
				Required   []string `json:"required"`
			} `json:"parameters"`
		} `json:"function"`
	} `json:"tools"`
	User string `json:"user"`
}

// --- Utility Functions ---

// ListDir 列出目录 dir 下的所有请求
func ListDir(dir string) ([]*DirRequest, error) {
	entries, err := os.ReadDir(dir)
	if err != nil {
		return nil, err
	}

	var dirRequests []*DirRequest
	for _, entry := range entries {
		if entry.IsDir() {
			subDir := entry.Name()
			files, err := os.ReadDir(filepath.Join(dir, subDir))
			if err != nil {
				return nil, err
			}

			var requestIndices []string
			for _, file := range files {
				if strings.HasSuffix(file.Name(), ".input.http") {
					index := strings.TrimSuffix(file.Name(), ".input.http")
					requestIndices = append(requestIndices, index)
				}
			}
			//sort requestIndices
			sort.Strings(requestIndices)
			dirRequests = append(dirRequests, &DirRequest{
				Dir:          subDir,
				RequestIndex: requestIndices,
			})

		}
	}
	sort.Slice(dirRequests, func(i, j int) bool {
		return dirRequests[i].Dir > dirRequests[j].Dir
	})
	return dirRequests, nil
}

// parseInput 解析 {dir}/{index}.input.http 文件，将其转换为 Input 格式
func parseInput(dir string, index int) (*Input, error) {
	filePath := filepath.Join(dir, fmt.Sprintf("%d.input.http", index))
	fileContent, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	req, err := http.ReadRequest(bufio.NewReader(bytes.NewReader(fileContent)))
	if err != nil {
		return nil, err
	}

	var input ChatInput
	if err := json.NewDecoder(req.Body).Decode(&input); err != nil {
		return nil, err
	}
	return toInput(input), nil
}

// parseOutput 解析 {dir}/{index}.output.http 文件，将其转换为 Output 格式
func parseOutput(dir string, index int) (*Output, error) {
	filePath := filepath.Join(dir, fmt.Sprintf("%d.output.http", index))
	fileContent, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	resp, err := http.ReadResponse(bufio.NewReader(bytes.NewReader(fileContent)), nil)
	if err != nil {
		return nil, err
	}

	var c *openai.ChatCompletion

	// 判断 {dir}/{index}.output.http 文件是否为流式响应
	isStream := strings.Contains(resp.Header.Get("Content-Type"), "text/event-stream")
	if isStream {
		// 先将流式响应解析成非流式的结果
		c, err = parseSSE(resp)

		if err != nil {
			return nil, err
		}
	} else {
		c, err = parseResponse(resp)
		if err != nil {
			return nil, err
		}
	}
	return toOutput(c)
}

func toOutput(c *openai.ChatCompletion) (*Output, error) {
	output := &Output{}
	if len(c.Choices) > 0 {
		choice := c.Choices[0]

		output.Messages = append(output.Messages, Message{
			Role:    string(choice.Message.Role),
			Content: string(choice.Message.Content),
		})
		for _, toolCall := range choice.Message.ToolCalls {
			output.ToolsCall = append(output.ToolsCall, ToolCall{
				Name: toolCall.Function.Name,
				Args: toolCall.Function.Arguments,
			})
		}
	}

	return output, nil
}

func toInput(c ChatInput) *Input {
	input := &Input{}
	for _, msg := range c.Messages {
		input.Messages = append(input.Messages, Message{
			Role:    msg.Role,
			Content: msg.Content,
		})
	}

	for _, tool := range c.Tools {
		input.Tools = append(input.Tools, Tool{
			Name: tool.Function.Name,
		})
	}
	return input
}

// parseSSE 解析 SSE, 提取 ChatCompletion
func parseSSE(resp *http.Response) (*openai.ChatCompletion, error) {
	stream := ssestream.NewStream[openai.ChatCompletionChunk](ssestream.NewDecoder(resp), nil)
	// optionally, an accumulator helper can be used
	acc := openai.ChatCompletionAccumulator{}
	for stream.Next() {
		chunk := stream.Current()
		acc.AddChunk(chunk)
	}
	if err := stream.Err(); err != nil {
		return nil, err
	}
	return &acc.ChatCompletion, nil
}

// parseResponse 解析响应报文，返回 ChatCompletion
func parseResponse(resp *http.Response) (*openai.ChatCompletion, error) {
	c := openai.ChatCompletion{}
	err := json.NewDecoder(resp.Body).Decode(&c)
	if err != nil {
		return nil, err
	}
	return &c, nil
}

func GetRequestByIndex(dir string, index int) (*Request, error) {
	input, err := parseInput(dir, index)
	if err != nil {
		log.Println("parseInput failed, err:", err)
		return nil, err
	}
	output, err := parseOutput(dir, index)
	if err != nil {
		return nil, err
	}

	return &Request{
		Input:  *input,
		Output: *output,
	}, nil
}

func Run(addr string) {

	router := mux.NewRouter()

	// API endpoints
	router.HandleFunc("/api/dir", func(writer http.ResponseWriter, request *http.Request) {
		dirs, _ := ListDir("output")
		respJson(writer, dirs)
	}).Methods("GET")

	router.HandleFunc("/api/dir/{dir}/{request_index}", func(writer http.ResponseWriter, req *http.Request) {

		dir := mux.Vars(req)["dir"]
		requestIndexStr := mux.Vars(req)["request_index"]
		requestIndex, _ := strconv.ParseInt(requestIndexStr, 10, 64)
		request, err := GetRequestByIndex(filepath.Join("output", dir), int(requestIndex))
		if err != nil {
			return
		}
		respJson(writer, request)
	}).Methods("GET")

	// Serve static files from the embedded filesystem
	distFS, err := fs.Sub(distDir, "frontend/build")
	if err != nil {
		log.Fatal(err)
	}
	// 创建自定义文件系统实例
	spaFs := &spaFileSystem{fs: http.FS(distFS)}
	// 为所有路径设置文件服务器
	router.PathPrefix("/").Handler(http.FileServer(spaFs))

	fmt.Println("visualize Listening on:", addr)
	http.ListenAndServe(addr, router)

}

func respJson(rw http.ResponseWriter, data any) {
	json.NewEncoder(rw).Encode(data)
}

// spaFileSystem 是一个自定义的文件系统，用于支持 SPA 路由
type spaFileSystem struct {
	fs http.FileSystem
}

// Open 实现 http.FileSystem 接口
// 如果请求的文件不存在，则返回 index.html
func (s *spaFileSystem) Open(name string) (http.File, error) {
	file, err := s.fs.Open(name)
	if err != nil {
		// 如果文件不存在，返回 index.html
		if errors.Is(err, fs.ErrNotExist) {
			return s.fs.Open("index.html")
		}
		return nil, err
	}
	return file, nil
}
