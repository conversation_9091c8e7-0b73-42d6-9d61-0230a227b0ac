import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

interface DirRequest {
  Dir: string;
  RequestIndex: string[];
}

const DirectoryTree: React.FC = () => {
  const [directories, setDirectories] = useState<DirRequest[]>([]);
  const [expandedDirs, setExpandedDirs] = useState<string[]>([]);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchDirectories = async () => {
      try {
        const response = await fetch('/api/dir');
        const data: DirRequest[] = await response.json();
        setDirectories(data);
      } catch (error) {
        console.error('Error fetching directories:', error);
      }
    };

    fetchDirectories();
  }, []);

  const toggleDirectory = (dir: string) => {
    if (expandedDirs.includes(dir)) {
      setExpandedDirs(expandedDirs.filter((d) => d !== dir));
    } else {
      setExpandedDirs([...expandedDirs, dir]);
    }
  };

  const handleRequestClick = (dir: string, index: string) => {
    navigate(`/dir/${dir}/${index}`);
  };

  return (
    <div>
      <h2>Directories</h2>
      <ul>
        {directories.map((dirReq) => (
          <li key={dirReq.Dir}>
            <span onClick={() => toggleDirectory(dirReq.Dir)}>
              {expandedDirs.includes(dirReq.Dir) ? '[-]' : '[+]'} {dirReq.Dir}
            </span>
            {expandedDirs.includes(dirReq.Dir) && (
              <ul>
                {dirReq.RequestIndex.map((index) => (
                  <li key={index} onClick={() => handleRequestClick(dirReq.Dir, index)}>
                    {index}
                  </li>
                ))}
              </ul>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default DirectoryTree;
