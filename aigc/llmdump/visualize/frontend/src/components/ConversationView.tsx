import React, { useState, useEffect } from 'react';
import Markdown from 'react-markdown';
import { useParams } from 'react-router-dom';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';

interface Message {
  Role: string;
  Content: string;
}

interface ToolCall {
  Name: string;
  Args: string;
}

interface Output {
  Messages: Message[];
  ToolsCall: ToolCall[];
}

interface Input {
  Messages: Message[];
  Tools?: any[];
}

interface Request {
  Input: Input;
  Output: Output;
}

const ConversationView: React.FC = () => {
  const [requestData, setRequestData] = useState<Request | null>(null);
  const { dir, requestIndex } = useParams<{ dir: string; requestIndex: string }>();

  useEffect(() => {
    const fetchRequestData = async () => {
      if (dir && requestIndex) {
        try {
          const response = await fetch(`/api/dir/${dir}/${requestIndex}`);
          const data: Request = await response.json();
          setRequestData(data);
        } catch (error) {
          console.error('Error fetching request data:', error);
        }
      }
    };

    fetchRequestData();
  }, [dir, requestIndex]);

  const getMessageStyle = (role: string) => {
    switch (role) {
      case 'system':
        return { backgroundColor: '#f0f0f0', color: 'black' };
      case 'user':
        return { backgroundColor: '#d0e9ff', color: 'black' };
      case 'assistant':
        return { backgroundColor: '#d0ffd0', color: 'black' };
      default:
        return {};
    }
  };

  return (
    <div>
      <h2>Conversation</h2>
      {requestData ? (
        <div>
          {requestData.Input.Messages.map((message, index) => (
            <div
              key={`input-${index}`}
              style={{ ...getMessageStyle(message.Role), border: '1px solid #ccc', padding: '10px', marginBottom: '10px' }}
            >
              <p>
                <strong>{message.Role}:</strong>
              </p>
              <pre style={{ whiteSpace: 'pre-wrap' }}>{message.Content}</pre>
            </div>
          ))}
          {requestData.Output.Messages &&
            requestData.Output.Messages.map((message, index) => (
              <div
                key={`output-${index}`}
                style={{ ...getMessageStyle(message.Role), border: '1px solid #ccc', padding: '10px', marginBottom: '10px' }}
              >
                <p>
                  <strong>{message.Role}:</strong>
                </p>
                <pre style={{ whiteSpace: 'pre-wrap' }}>{message.Content}</pre>
              </div>
            ))}
          {requestData.Output.ToolsCall &&
            requestData.Output.ToolsCall.map((toolCall, index) => (
              <div key={`toolcall-${index}`} style={{ border: '1px solid #ccc', padding: '10px', marginBottom: '10px' }}>
                <p>
                  <strong>Tool Call:</strong>
                </p>
                <p>Name: {toolCall.Name}</p>
                <p>Args: {toolCall.Args}</p>
              </div>
            ))}
        </div>
      ) : (
        <p>Select a request to view the conversation.</p>
      )}
    </div>
  );
};

export default ConversationView;
