import React from 'react';
import './App.css';
import DirectoryTree from './components/DirectoryTree';
import ConversationView from './components/ConversationView';
import { Routes, Route } from 'react-router-dom';

function App() {
  return (
    <div className="App">
      <div className="sidebar">
        <DirectoryTree />
      </div>
      <div className="main-content">
        <Routes>
          <Route path="/" element={<p>Please select a request.</p>} />
          <Route path="/dir/:dir/:requestIndex" element={<ConversationView />} />
        </Routes>
      </div>
    </div>
  );
}

export default App;
