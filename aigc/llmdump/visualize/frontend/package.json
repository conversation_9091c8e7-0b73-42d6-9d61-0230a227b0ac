{"name": "llmdebug-web", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.3.0", "react-scripts": "5.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://127.0.0.1:8080"}