
llmdebug-web 是一个显示 llm 对话消息的 web 页面。


## API

```yaml
openapi: 3.0.0
info:
  title: LLMDump Visualize API
  version: v1

paths:
  /api/dir:
    get:
      summary: List directories
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DirRequest'

  /api/dir/{dir}/{request_index}:
    get:
      summary: Get request by index
      parameters:
        - in: path
          name: dir
          required: true
          schema:
            type: string
          description: Directory name
        - in: path
          name: request_index
          required: true
          schema:
            type: integer
          description: Request index
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Request'
components:
  schemas:
    DirRequest:
      type: object
      properties:
        Dir:
          type: string
        RequestIndex:
          type: array
          items:
            type: string

    Request:
      type: object
      properties:
        Input:
          $ref: '#/components/schemas/Input'
        Output:
          $ref: '#/components/schemas/Output'

    Input:
      type: object
      properties:
        Messages:
          type: array
          items:
            $ref: '#/components/schemas/Message'
        Tools:
          type: array
          items:
            $ref: '#/components/schemas/Tool'

    Tool:
      type: object
      properties:
        Name:
          type: string

    Message:
      type: object
      properties:
        Role:
          type: string
        Content:
          type: string

    Output:
      type: object
      properties:
        Messages:
          type: array
          items:
            $ref: '#/components/schemas/Message'
        ToolsCall:
          type: array
          items:
            $ref: '#/components/schemas/ToolCall'

    ToolCall:
      type: object
      properties:
        Name:
          type: string
        Args:
          type: string

    ChatInput:
      type: object
      properties:
        Model:
          type: string
        Stream:
          type: boolean
        Messages:
          type: array
          items:
            type: object
            properties:
              Role:
                type: string
              Content:
                type: string
        ToolChoice:
          type: string
        Tools:
          type: array
          items:
            type: object
            properties:
              Type:
                type: string
              Function:
                type: object
                properties:
                  Name:
                    type: string
                  Description:
                    type: string
                  Parameters:
                    type: object
                    properties:
                      Type:
                        type: string
                      Properties:
                        type: object
                      Required:
                        type: array
                        items:
                          type: string

        User:
          type: string
```

API 运行在 127.0.0.1:8080，可以通过代理来访问它。

## 页面 

这个页面有二栏:

第一栏显示 读取 `GET /api/dir` 这个接口，显示一个树形的列表，这个树形其实就有两层。 

1. 点击文件夹展开下面的节点。
2. 点击叶子节点切换第二栏的内容。


第二栏以模拟对话的形式显示接口 `GET /api/dir/{dir}/{request_index}` 返回的结果, content 字段内容需要以 markdown 格式显示。

1. message中的 Role 有 `system`， `user` 或者  `assistant`， 分别显示不同的颜色。

其它要求：

1. 点击文件夹展开。
2. 文件会高亮显示。
4. 页面简洁但需要有设计感。


工程要求：

使用 reactjs 实现这个 web 应用。

