// 提示词：
// 实现一个 OpenAI 的 http 反向代理服务
// 打印每次请求的 Message 信息
// 打印每次响应的 Message 信息

package main

import (
	"bytes"
	"flag"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"net/http/httputil"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"codebase.zhonganinfo.com/zainfo/cube-kits/aigc/llmdump/visualize"
)

type providerConfig struct {
	Endpoint string `json:"endpoint"`
	LLM      string
}

var wellknowTargets = map[string]providerConfig{
	"openai":      {"https://api.openai.com/v1", "gpt-4o"},
	"bigmodel":    {"https://open.bigmodel.cn/api/paas/v4", "glm-4-plus"},
	"deepseek":    {"https://api.deepseek.com/v1", "deepseek-chat"},
	"ollama":      {"http://127.0.0.1:11434", ""},
	"siliconflow": {"https://api.siliconflow.cn", "deepseek-ai/DeepSeek-V3"},
	"ark":         {"https://ark.cn-beijing.volces.com/api/v3", "DeepSeek-R1"},
	"baidu":       {"https://qianfan.bj.baidubce.com/v2", "ernie-4.5-8k-preview"},
	"aliyun":      {"https://dashscope.aliyuncs.com/compatible-mode/v1", "qwen-long"},
}

func getApiKey(target string) string {

	if _, ok := wellknowTargets[target]; ok {
		k := os.Getenv(strings.ToUpper(target + "_LLM_API_KEY"))
		if k != "" {
			return k
		}
	}
	return os.Getenv("LLM_API_KEY")
}

var target = flag.String("target", "https://open.bigmodel.cn/api/paas/v4", "代理 LLM 目标地址，包含 Path 部分, 默认支持 openai, bigmodel, deepseek, ollama, ark, baidu, 其它请手动指定，如 https://open.bigmodel.cn/api/paas/v4。")
var llmName = flag.String("llm", "", "手动指定模型名称，覆盖 target 默认的模型")
var bind = flag.String("bind", ":8888", "代理的绑定地址")
var provider = flag.String("provider", "openai", "代理的供应商，目前仅支持 ollama 和 openai")
var uiBind = flag.String("ui", ":8081", "web 查看页面的绑定地址")

type chatThreadDumpTransport struct {
	index int
	http.RoundTripper
	thread         []string
	decodeRequest  func([]byte) string
	decodeResponse func([]byte) string
	handle         func(msg string, req bool, inc bool)
	msgStore       MessageStore
	debugOutputDir string
}

func NewDefaultTransport(provider string) *chatThreadDumpTransport {

	fs := fileStore{
		index: 0,
		dir:   filepath.Join("output", time.Now().Format("2006-01-02 15:04:05")),
	}

	return &chatThreadDumpTransport{
		RoundTripper: http.DefaultTransport,
		msgStore:     &fs,
	}
}

type MessageStore interface {
	SaveMessage(req, resp []byte)
}

type fileStore struct {
	index int
	dir   string
}

func (fs *fileStore) SaveMessage(req, resp []byte) {
	if err := os.MkdirAll(fs.dir, 0777); err != nil {
		panic(err)
	}

	if err := os.WriteFile(filepath.Join(fs.dir, fmt.Sprintf("%d.input.http", fs.index)), req, 0644); err != nil {
		fmt.Println("write file error:", err)
	}
	if err := os.WriteFile(filepath.Join(fs.dir, fmt.Sprintf("%d.output.http", fs.index)), resp, 0644); err != nil {
		fmt.Println("write file error:", err)
	}
	fs.index++
}

func (t *chatThreadDumpTransport) RoundTrip(r *http.Request) (*http.Response, error) {
	intput, _ := httputil.DumpRequest(r, true)

	resp, err := t.RoundTripper.RoundTrip(r)
	if err != nil {
		return nil, err
	}
	output, _ := httputil.DumpResponse(resp, true)
	t.msgStore.SaveMessage(intput, output)

	return resp, err
}

type ReadCloser struct {
	io.Reader
}

func (*ReadCloser) Close() error {
	return nil
}

func main() {
	flag.Parse()
	// 目标服务器地址，这里假设 OpenAI 服务的 URL

	apiKey := getApiKey(*target)
	fmt.Println("apikey:", apiKey)

	if t, ok := wellknowTargets[*target]; ok {
		*target = t.Endpoint
		if *llmName == "" {
			*llmName = t.LLM
		}
	}

	proxyURL, err := url.Parse(*target)
	if err != nil {
		log.Fatal("Error parsing proxy target URL:", err)
	}

	fmt.Println("proxy target endpoint:", proxyURL.String())
	fmt.Println("proxy target llm:", *llmName)
	proxy := httputil.NewSingleHostReverseProxy(proxyURL)
	//originalDirector := proxy.Director
	proxy.Director = func(req *http.Request) {
		// 在这里打印每次请求的 Message 信息
		log.Printf("Request Message: %v %v\n", req.Method, req.URL)
		req.URL.Host = proxyURL.Host
		req.Host = proxyURL.Host
		req.URL.Scheme = proxyURL.Scheme
		if apiKey != "" {
			req.Header.Set("Authorization", "Bearer "+apiKey)
		}
		bs, _ := io.ReadAll(req.Body)

		if *llmName != "" {
			bs = []byte(ReplaceModelSimple(string(bs), *llmName))
		}

		req.URL.Path = strings.TrimSuffix(proxyURL.Path, "/") + req.URL.Path
		if !strings.Contains(string(bs), `"messages":`) {
			req.URL.Path = "/api/paas/v4/embeddings"
		}
		log.Printf("Proxy to Request Message: %v %v\n", req.Method, req.URL)

		req.ContentLength = 0

		req.RequestURI = req.URL.RequestURI()
		req.ContentLength = int64(len(bs))
		req.Body = &ReadCloser{Reader: bytes.NewBuffer(bs)}
		//originalDirector(req)
	}

	// 修改代理的 Transport 来拦截响应
	originalTransport := proxy.Transport
	if originalTransport == nil {
		originalTransport = http.DefaultTransport
	}
	proxy.Transport = NewDefaultTransport(*provider)

	go func() {
		visualize.Run(*uiBind)
	}()

	ips, _ := GetLocalIPs()
	log.Printf(" Starting llm proxy on: %v, %v...", ips, *bind)
	// 启动 http 服务器
	if err := http.ListenAndServe(*bind, proxy); err != nil {
		log.Fatal("Error starting proxy server:", err)
	}
}

// GetLocalIPs returns a list of the machine's local non-loopback IP addresses.
func GetLocalIPs() ([]string, error) {
	var ips []string
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return nil, err
	}

	for _, address := range addrs {
		// Check the address type and if it is not a loopback, then display it
		if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil { // Prioritize IPv4
				ips = append(ips, ipnet.IP.String())
			}
		}
	}
	// if no IPv4, then get IPv6.
	if len(ips) == 0 {
		for _, address := range addrs {
			if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
				if ipnet.IP.To16() != nil {
					ips = append(ips, ipnet.IP.String())
				}
			}
		}

	}

	return ips, nil
}

// ReplaceModelSimple 替换字符串中第一个匹配的 "model":"..." 中的模型名称
//
//	只接受 input 和 newModel，它会找到并替换第一个 "model" 对应的值。
//
// 不使用正则表达式
func ReplaceModelSimple(input, newModel string) string {
	target := `"model":`
	index := strings.Index(input, target)

	if index == -1 {
		return input // 没有找到，直接返回
	}

	valueStart := index + len(target)
	// 找到值的起始引号 (跳过可能的空格)
	for valueStart < len(input) && input[valueStart] == ' ' {
		valueStart++
	}

	if valueStart >= len(input) || input[valueStart] != '"' {
		return input // 格式不正确，直接返回
	}
	valueStart++ // 跳过起始引号

	valueEnd := strings.IndexByte(input[valueStart:], '"')
	if valueEnd == -1 {
		return input // 格式不正确，没有结束引号，直接返回
	}
	valueEnd += valueStart // 加上偏移量

	return input[:valueStart] + newModel + input[valueEnd:]
}
