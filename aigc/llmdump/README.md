# llmdump

llmdump 代理大模型接口,对所有的请求进行抓包，然后使用 web 界面查看，方便逆向 AI 应用。

## 安装 
```
make
```

## 快速开始

启动程序，代理 `https://open.bigmodel.cn/api/paas/v4` 接口
```
./llmdump -target https://open.bigmodel.cn/api/paas/v4
```

### 请求 AI API

```
## Mock Conversation
curl -X "POST" "http://localhost:8888/chat/completions" \
     -H 'Content-Type: application/json' \
     -H 'Accept: application/json' \
     -H 'Authorization: Bearer "YOUR KEY"' \
     -H 'Cookie: acw_tc=0aef812717397846157636534e00ef2519ceeae58b7ca4bfae0937a6beb1e1' \
     -d $'{
  "model": "glm-4-flash",
  "stream": true,
  "messages": [
    {
      "content": "《百年孤独》的作者是哥伦比亚作家加夫列尔·加西亚·马尔克斯（<PERSON>）。这部小说是拉丁美洲魔幻现实主义文学的代表作之一，对20世纪文学产生了深远的影响。",
      "role": "assistant"
    },
    {
      "content": "故事的主人翁叫什么？",
      "role": "user"
    }
  ]
}'

```

### 查看请求 UI

```
open http://127.0.0.1:8081
```