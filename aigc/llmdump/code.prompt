## 功能简介

代理大模型接口, 对所有的请求进行抓包，然后使用 web 界面查看，方便逆向 AI 应用.

## 使用方式

```
llmdump -target https://open.bigmodel.cn/api/paas/v4
```

## 帮助

```
Usage of ./llmdump:
  -bind string
        代理的绑定地址 (default ":8888")
  -provider string
        代理的供应商，目前仅支持 ollama 和 openai (default "openai")
  -target string
        代理 LLM 目标地址，包含 Path 部分 (default " https://open.bigmodel.cn/api/paas/v4")
  -ui string
        web 查看页面的绑定地址 (default ":8081")
```

## 功能详细说明

- llmdump 代理支持将流式响应的结果进行组合，形成完成的消息格式。
- llmdump 支持ollama和openai 两种提供商。
- llmdump 将抓包的数据存储在 output/${date}/${auto_incurement}.json
- web 页面包含两栏，左侧是 output 树形结构，点击显示 `${auto_incurement}.json` 的会话，以模拟消息聊天的形式显示。

## 工程要求

- 使用 go 实现代理 
- 使用 go 实现 web 工程的静态文件代理，以及提供 API 接口请求 output 目录。
- 使用 embedfs 将 web 工程的 dist 目录打包到 go 应用里。
- web 使用 react 实现。
- web ui 使用 headless ui shadcn 库来实现。
- web 中的聊天支持 markdown 格式的切换, markdown 支持 代码高亮，表格显示等特性。


## 样例说明

`${auto_incurement}.json` 的内容如下：

```
[
  {
    "role": "assistant",
    "content": "《百年孤独》的作者是哥伦比亚作家加夫列尔·加西亚·马尔克斯（Gabriel García Márquez）。这部小说是拉丁美洲魔幻现实主义文学的代表作之一，对20世纪文学产生了深远的影响。"
  },
  {
    "role": "user",
    "content": "故事的主人翁叫什么？"
  },
  {
    "role": "assistant",
    "content": "《百年孤独》的故事围绕布恩迪亚家族展开，主要的主人公是何塞·阿卡蒂奥·布恩迪亚（José Arcadio Buendía）。他是乌尔苏拉和梅尔基亚德斯收养的第一个孩子，也是布恩迪亚家族的第一代创始人。他后来有了多个同名儿子，如第二代的何塞·阿卡蒂奥以及第三代的何塞·阿卡蒂奥·布恩迪亚二世。书中的人物众多，每个成员都在家族的历史中扮演着重要角色。"
  }
]
```

output 的目录结构如下：

```
output
└── 2025-02-16\ 17:30:31
    └── 1.json
    └── 2.json
└── 2025-02-17\ 17:30:31
    └── 1.json
    └── 2.json
```