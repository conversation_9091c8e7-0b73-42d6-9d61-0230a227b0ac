openapi: "3.0.0"
info:
  version: 1.0.0
  title: Agent Data Tools
servers:
  - url: http://*************:8080
paths:
  /v1/create_json_data:
    post:
      summary: Creates JSON data (array format, even for a single item).
      description: |
        This endpoint allows you to create new JSON data within a dataset. The data must be provided in JSON array format, even if you are only creating a single JSON object. This ensures consistency across all data operations.

        **Example Request Body:**

        ```json
        {
          "data": "[{\"name\": \"example\", \"value\": 123}]",
          "schema_id": "schema_456",
          "dataset_id": "dataset_123"
        }
        ```
      operationId: CREATE_JSON_DATA
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  description: |
                    JSON data in array format.
                    **Example:** `"[{\"key1\": \"value1\", \"key2\": \"value2\"}]"` or `"[{\"item\": 1}]"`
                  type: string
                schema_id:
                  description: |
                    ID for the JSON schema that the data conforms to. This ID is used to validate the structure of the provided JSON data.
                    **Example:** `"schema_123"`
                  type: string
                dataset_id:
                  description: |
                    ID for the new dataset where the JSON data will be created. This ID will uniquely identify the dataset.
                    **Example:** `"dataset_abc"`
                  type: string
              required:
                - data
                - schema_id
                - dataset_id

  /v1/replace_json_data:
    post:
      summary: Modifies JSON data using JSON diff format.
      description: |
        This endpoint modifies existing JSON data within a dataset by applying a JSON diff. JSON diff is a standardized format for describing changes between two JSON documents. This method is efficient for updating parts of a large JSON object without sending the entire object again.

        **Example Request Body:**

        ```json
        {
          "json_diff": "[{\"op\": \"replace\", \"path\": \"/items/0/value\", \"value\": \"new_value\"}]",
          "schema_id": "schema_456",
          "dataset_id": "dataset_123"
        }
        ```

        **Note:** For information on JSON diff format, refer to [RFC6902 - JSON Patch](https://datatracker.ietf.org/doc/html/rfc6902).
      operationId: REPLACE_JSON_DATA
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                json_diff:
                  description: |
                    JSON diff to update the data. Follows the JSON Patch format (RFC6902).
                    **Example:** `"[{\"op\": \"replace\", \"path\": \"/key\", \"value\": \"new_value\"}]"`
                  type: string
                schema_id:
                  description: |
                    ID for the JSON schema associated with the dataset being updated. Ensures the diff is compatible with the schema.
                    **Example:** `"schema_789"`
                  type: string
                dataset_id:
                  description: |
                    ID of the dataset containing the JSON data to be updated.
                    **Example:** `"dataset_xyz"`
                  type: string
              required:
                - json_diff
                - schema_id
                - dataset_id

  /v1/append_json_data:
    post:
      summary: Appends data to an existing dataset (array format, even for a single item).
      description: |
        This endpoint appends new JSON data to an existing dataset.  Similar to the create endpoint, the data must be provided in JSON array format for consistency.  This operation adds to the existing data in the dataset without replacing it.

        **Example Request Body:**

        ```json
        {
          "data": "[{\"new_item\": \"appended_data\"}]",
          "schema_id": "schema_456",
          "dataset_id": "dataset_123"
        }
        ```
      operationId: APPEND_JSON_DATA
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  description: |
                    JSON data to append to the dataset, provided in array format.
                    **Example:** `"[{\"new_key\": \"new_value\"}]"` or `"[{\"single_item\": true}]"`
                  type: string
                schema_id:
                  description: |
                    ID for the JSON schema associated with the dataset. The appended data must conform to this schema.
                    **Example:** `"schema_abc"`
                  type: string
                dataset_id:
                  description: |
                    ID of the dataset to which the JSON data will be appended.
                    **Example:** `"dataset_pqr"`
                  type: string
              required:
                - data
                - schema_id
                - dataset_id

  /v1/task_complete:
    get:
      summary: Notifies when a task is complete.
      description: |
        This endpoint is used to signal that a background task has finished. The `status` parameter indicates the outcome of the task.

        **Example Request:**

        `/v1/task_complete?status=done`
      operationId: TASK_COMPLETE
      parameters:
        - name: status
          in: query
          description: |
            Indicates the status of the task.  Use `done` for successful completion. Other values may be used for errors or different states, but `done` is the primary success indicator.
            **Example:** `"done"`
          required: true
          schema:
            type: string

  /v1/ask_followup_question:
    post:
      summary: Asks follow-up questions to clarify user input.
      description: |
        This endpoint is used to request clarification from the user when the initial input is ambiguous or incomplete. The service will respond with a specific question that the agent should ask the user to gather more information.

        **Example Request Body:**

        ```json
        {
          "question": "Could you please specify the data type for the 'value' field?"
        }
        ```

        **Example Response Body (from the service to the agent - not part of OpenAPI spec but illustrative):**

        ```json
        {
          "question": "Could you please specify the data type for the 'value' field?"
        }
        ```
      operationId: ASK_FOLLOWUP_QUESTION
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                question:
                  description: |
                    Specific question to ask the user for more information. This question should be clear and concise to help the user provide the necessary details.
                    **Example:** `"Which dataset do you want to update?"`
                  type: string
              required:
                - question
  /v1/database/query:
    get:
      summary: query database to answer user question
      description: query database to answer user question
      operationId: DATABASE_QUERY
      parameters:
        - name: sql
          in: query
          description: sql to execute
          required: true
          schema:
            type: string