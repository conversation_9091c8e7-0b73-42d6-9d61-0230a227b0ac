{"openapi": "3.1.0", "info": {"title": "Get weather data", "description": "Retrieves current weather data for a location.", "version": "v1.0.0"}, "servers": [{"url": "http://*************:2345"}], "paths": {"/v1/create_json_data": {"get": {"description": "create_json_data", "operationId": "CreateJsonData", "parameters": [{"name": "data", "in": "query", "description": "创建 json 格式的数据，data应该是数组形式，即使是单条数组，也使用一元数组的格式", "required": true, "schema": {"type": "string"}}]}}, "/v1/replace_json_data": {"get": {"description": "以 jsondiff 的格式修改 json 数据", "operationId": "replace_json_data", "parameters": [{"name": "data", "in": "query", "description": "使用 jsondiff 的格式修改 json data", "required": true, "schema": {"type": "string"}}]}}, "/v1/task_complete": {"get": {"description": "如果用户指定的任务完成了，通过此工具来告知用户", "operationId": "task_complete", "parameters": [{"name": "status", "in": "query", "description": "yes:完成，no:未完成", "required": true, "schema": {"type": "string"}}]}}}, "components": {"schemas": {}}}