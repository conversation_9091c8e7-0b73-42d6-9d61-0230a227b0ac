# 基于 diff 的 canvas form 及 Agent 实现

## 背景

从 UI 表现上， canvas form 会将 Agent 始终展示在一个 table 中。

比如将 Agent 生成测试用例时，对话框的右侧会出一个 table, 里面展示生成的测试用例数据。

在用户与 Agent的继续的对话中，Agent 会自主的对 Table 进行编辑。比如

1. 用户要求生成更多的测试用例，此时 Table 里面会追加新的测试用例。
2. 用户要求修改指定的数据，此时可以做到局部修改单元格。
3. 用户认为让为测试用例不满足，可以要求删除行。

## Agent 设计
### Agent 的提示词设计

```markdown
你是一名专业的测试架构师，擅长将需求转化为多维度的测试场景。请根据以下规则生成结构化测试用例：

# 输入处理

1. 需求分析
- 解析用户输入的业务需求
- 识别被测系统类型（Web/APP/API/数据库等）
- 提取核心功能点和业务规则
- 标注潜在风险点（资金/数据/权限等敏感领域）
2. 要素提取
- 明确测试范围（包含/排除项）
- 识别用户角色和权限层级
- 确认业务流程关键节点
- 发现业务规则边界条件

# 测试策略

1. 生成维度
├─ 功能测试（正常/异常路径）
├─ 性能测试（负载/压力/稳定性）*
├─ 安全测试（OWASP TOP10相关项）*
└─ 回归测试（关联功能影响域）
* 标星号维度需风险评估后生成

# 数据格式

1. 输出规范
使用以下 json schema 输出。
<json_schema schema_id="{{schema_id}}">
{
    "$schema": "http://json-schema.org/draft-07/schema#",
    "type": "object",
    "properties":
    {
        "id":
        {
            "type": "string",
            "description": "The unique identifier of the test case"
        },
        "title":
        {
            "type": "string",
            "description": "A brief title or description of the test case"
        },
        "description":
        {
            "type": "string",
            "description": "A detailed description of what the test case does"
        },
        "steps":
        {
            "type": "array",
            "description": "A list of steps to execute for the test case",
            "items":
            {
                "type": "string",
                "description": "Each step in the test case execution"
            }
        },
        "expected_result":
        {
            "type": "string",
            "description": "The expected result after executing the steps"
        },
        "actual_result":
        {
            "type": "string",
            "description": "The actual result after executing the steps"
        },
        "status":
        {
            "type": "string",
            "enum":
            [
                "pass",
                "fail",
                "not executed"
            ],
            "description": "The status of the test case"
        },
        "priority":
        {
            "type": "string",
            "enum":
            [
                "low",
                "medium",
                "high"
            ],
            "description": "The priority level of the test case"
        }
    },
    "required":
    [
        "id",
        "title",
        "description",
        "steps",
        "expected_result",
        "priority",
    ]
}
</json_schema>

4. 数据集 dataset
生成数据使用指定数据集： dataset_id={{dataset_id}}

# 交互机制
5. 追问逻辑
当检测到以下情况时请求补充信息：
- 业务规则存在二义性
- 缺少边界值定义
- 用户权限体系不明确
- 性能指标未量化
- 兼容性范围未指定
用户可以针对生成的用例提出修改意见，或者新的用例生成需求。

# 工具使用指导

- 使用 CREATE_JSON_DATA 创建测试用例，数据格式满足上述 {{schema_id}}。此工具用来替换所有的数据，适用第一次生成或者重新生成数据的情况。
- 使用 REPLACE_JSON_DATA 修改测试用例，使用 jsondiff 格式来修改测试用例。此工具适合用来局部修改数据。
- 使用 APPEND_JSON_DATA 追加测试用例，数据格式满足上述 json schema。此工具适合用来增量的添加新的数据。
- 使用 TASK_COMPLETE 通知用户生成测试用例的任务完成。
- 只允许使用 ASK_FOLLOWUP_QUESTION 工具向用户提问。 仅当您需要其他详细信息才能完成任务时才使用此工具，并确保使用清晰简洁的问题，这将有助于您继续执行任务。 但是，如果您可以使用可用的工具来避免不得不向用户提问，则应这样做。 例如，如果用户提到了一个你无法理解的知识，你应该调用此功能询问用户"

# 规则

1. 每次回复都应该调用工具。
2. **如果认为任务已经完成，请调用工具 TASK_COMPLETE.**

# 目标

您迭代地完成生成测试用例的任务，将其分解为清晰的步骤并有条不紊地完成它们。
1. 分析用户的任务并设定明确、可实现的目标以完成它。 以逻辑顺序优先考虑这些目标。
2. 依次完成这些目标，根据需要一次使用一个可用的工具。 每个目标都应对应于您解决问题的过程中的一个不同步骤。 您将随时了解已完成的工作和剩余的工作。
3. 请记住，您拥有广泛的功能，可以根据需要巧妙地使用各种工具来完成每个目标。 在调用工具之前，请在 <thinking></thinking> 标签中进行一些分析。
4. 必须使用 TASK_COMPLETE 工具通知用户已完成，否则会导致用户无限等待。
5. 用户可能会提供反馈，您可以利用这些反馈进行改进并再次尝试。 但不要继续进行毫无意义的来回对话，即不要以问题或提供进一步帮助来结束您的回复。

```

在这个提示词模板中，我们先忽略跟测试业务有关的部分，重点关注以下部分：

1. 定义了 json schema 格式
2. 定义了 dataset id.
3. 定义了 工具使用指导.

### Agent 工具定义

- CREATE_JSON_DATA：在指定的数据集中,创建指定的符合 Schema 的数据。
- REPLACE_JSON_DATA: 局部更新指定数据集中的数据，主要使用 jsondiff 这个工具来实现的，这个工具在 kustomize 有使用。
- APPEND_JSON_DATA: 增量添加数据

详细的工具接口说明参考以下文档。

```yaml
openapi: "3.0.0"
info:
  version: 1.0.0
  title: Agent Data Tools
servers:
  - url: http://*************:8080
paths:
  /v1/create_json_data:
    post:
      summary: Creates JSON data (array format, even for a single item).
      description: |
        This endpoint allows you to create new JSON data within a dataset. The data must be provided in JSON array format, even if you are only creating a single JSON object. This ensures consistency across all data operations.

        **Example Request Body:**

        ```json
        {
          "data": "[{\"name\": \"example\", \"value\": 123}]",
          "schema_id": "schema_456",
          "dataset_id": "dataset_123"
        }
        ```
      operationId: CREATE_JSON_DATA
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  description: |
                    JSON data in array format.
                    **Example:** `"[{\"key1\": \"value1\", \"key2\": \"value2\"}]"` or `"[{\"item\": 1}]"`
                  type: string
                schema_id:
                  description: |
                    ID for the JSON schema that the data conforms to. This ID is used to validate the structure of the provided JSON data.
                    **Example:** `"schema_123"`
                  type: string
                dataset_id:
                  description: |
                    ID for the new dataset where the JSON data will be created. This ID will uniquely identify the dataset.
                    **Example:** `"dataset_abc"`
                  type: string
              required:
                - data
                - schema_id
                - dataset_id

  /v1/replace_json_data:
    post:
      summary: Modifies JSON data using JSON diff format.
      description: |
        This endpoint modifies existing JSON data within a dataset by applying a JSON diff. JSON diff is a standardized format for describing changes between two JSON documents. This method is efficient for updating parts of a large JSON object without sending the entire object again.

        **Example Request Body:**

        ```json
        {
          "json_diff": "[{\"op\": \"replace\", \"path\": \"/items/0/value\", \"value\": \"new_value\"}]",
          "schema_id": "schema_456",
          "dataset_id": "dataset_123"
        }
        ```

        **Note:** For information on JSON diff format, refer to [RFC6902 - JSON Patch](https://datatracker.ietf.org/doc/html/rfc6902).
      operationId: REPLACE_JSON_DATA
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                json_diff:
                  description: |
                    JSON diff to update the data. Follows the JSON Patch format (RFC6902).
                    **Example:** `"[{\"op\": \"replace\", \"path\": \"/key\", \"value\": \"new_value\"}]"`
                  type: string
                schema_id:
                  description: |
                    ID for the JSON schema associated with the dataset being updated. Ensures the diff is compatible with the schema.
                    **Example:** `"schema_789"`
                  type: string
                dataset_id:
                  description: |
                    ID of the dataset containing the JSON data to be updated.
                    **Example:** `"dataset_xyz"`
                  type: string
              required:
                - json_diff
                - schema_id
                - dataset_id

  /v1/append_json_data:
    post:
      summary: Appends data to an existing dataset (array format, even for a single item).
      description: |
        This endpoint appends new JSON data to an existing dataset.  Similar to the create endpoint, the data must be provided in JSON array format for consistency.  This operation adds to the existing data in the dataset without replacing it.

        **Example Request Body:**

        ```json
        {
          "data": "[{\"new_item\": \"appended_data\"}]",
          "schema_id": "schema_456",
          "dataset_id": "dataset_123"
        }
        ```
      operationId: APPEND_JSON_DATA
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  description: |
                    JSON data to append to the dataset, provided in array format.
                    **Example:** `"[{\"new_key\": \"new_value\"}]"` or `"[{\"single_item\": true}]"`
                  type: string
                schema_id:
                  description: |
                    ID for the JSON schema associated with the dataset. The appended data must conform to this schema.
                    **Example:** `"schema_abc"`
                  type: string
                dataset_id:
                  description: |
                    ID of the dataset to which the JSON data will be appended.
                    **Example:** `"dataset_pqr"`
                  type: string
              required:
                - data
                - schema_id
                - dataset_id

  /v1/task_complete:
    get:
      summary: Notifies when a task is complete.
      description: |
        This endpoint is used to signal that a background task has finished. The `status` parameter indicates the outcome of the task.

        **Example Request:**

        `/v1/task_complete?status=done`
      operationId: TASK_COMPLETE
      parameters:
        - name: status
          in: query
          description: |
            Indicates the status of the task.  Use `done` for successful completion. Other values may be used for errors or different states, but `done` is the primary success indicator.
            **Example:** `"done"`
          required: true
          schema:
            type: string

  /v1/ask_followup_question:
    post:
      summary: Asks follow-up questions to clarify user input.
      description: |
        This endpoint is used to request clarification from the user when the initial input is ambiguous or incomplete. The service will respond with a specific question that the agent should ask the user to gather more information.

        **Example Request Body:**

        ```json
        {
          "question": "Could you please specify the data type for the 'value' field?"
        }
        ```

        **Example Response Body (from the service to the agent - not part of OpenAPI spec but illustrative):**

        ```json
        {
          "question": "Could you please specify the data type for the 'value' field?"
        }
        ```
      operationId: ASK_FOLLOWUP_QUESTION
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                question:
                  description: |
                    Specific question to ask the user for more information. This question should be clear and concise to help the user provide the necessary details.
                    **Example:** `"Which dataset do you want to update?"`
                  type: string
              required:
                - question
```


### Agent 如何来定义 Schema ID 和 Dataset ID

扩展 Agent 的参数属性，让其具备默认值。默认值支持特殊函数，如随机数。对于有默认值的数据，支持在前端隐藏展示。

Agent 添加以下三个参数：
1. json_schema: 默认值在设计 Agent时填写好。
2. json_schema_id_x: 给每个 schema 定义一个唯一的 id。
3. dataset_id_x: 每次会话的 

### json_schema_id 和 dataset_id

1. 工具需要通过 json_schema_id 来获取对应的 json_schema， 在提交数据时，需要先较验数据格式是否正确。
2. 工具需要通过 dataset_id 来维护本轮对话的

### final data

针对每次修后的数据，需要将 final data 返回给模型，并要求后续所有针对数据的操作基于此数据进行。
由于 final data 允许用户编辑，每次用户发起会话时也需要携带此数据。

```
请继续生成一些反向的用例。
<final_data>

</final_data>
```

### 前端如何实现 Canvas Form?

1. 唤起 Canvas Form 。

dify 的 SSR 事件会完整的接收到工具调用事件，如下：
```json
data:{
  "event": "agent_thought",
  "conversation_id": "8419c89c-567c-469b-9a8c-3a8acad993fb",
  "message_id": "e9b81523-a1f6-4709-8db9-6d87b9888c4c",
  "created_at": 1740080802,
  "task_id": "698d6dcf-1d67-47e6-a2e4-b5f429d8a7a9",
  "id": "859b4479-1fb8-47de-af17-3a66b807cd77",
  "position": 1,
  "thought": "<thinking>\n首先，我需要分析用户的需求，即针对手机验证码登录场景设计测试用例。这个场景涉及到用户登录功能，属于Web应用的一部分。核心功能点包括手机号码输入、验证码发送、验证码输入和登录验证。潜在风险点包括验证码的安全性和用户隐私保护。\n\n接下来，我需要提取测试要素，包括测试范围（手机验证码登录功能）、用户角色（普通用户）、业务流程关键节点（验证码发送和验证）、业务规则边界条件（验证码的有效期和输入次数限制）。\n\n基于这些分析，我将生成功能测试、性能测试、安全测试和回归测试的用例。功能测试将覆盖正常和异常路径，性能测试将关注验证码发送的响应时间，安全测试将关注验证码的安全性，回归测试将确保新功能不影响现有登录功能。\n</thinking>",
  "observation": "",
  "tool": "CREATE_JSON_DATA",
  "tool_labels": {
    "CREATE_JSON_DATA": {
      "en_US": "CREATE_JSON_DATA",
      "zh_Hans": "CREATE_JSON_DATA"
    }
  },
  "tool_input": "{\"CREATE_JSON_DATA\": {\"data\": \"[{\\\"id\\\": \\\"TC001\\\", \\\"title\\\": \\\"验证手机号码格式正确\\\", \\\"description\\\": \\\"验证用户输入的手机号码格式是否正确\\\", \\\"steps\\\": [\\\"打开登录页面\\\", \\\"输入有效的手机号码\\\", \\\"点击获取验证码\\\"], \\\"expected_result\\\": \\\"验证码成功发送\\\", \\\"actual_result\\\": \\\"\\\", \\\"status\\\": \\\"not executed\\\", \\\"priority\\\": \\\"high\\\"}, {\\\"id\\\": \\\"TC002\\\", \\\"title\\\": \\\"验证手机号码格式错误\\\", \\\"description\\\": \\\"验证用户输入的手机号码格式错误时的情况\\\", \\\"steps\\\": [\\\"打开登录页面\\\", \\\"输入无效的手机号码\\\", \\\"点击获取验证码\\\"], \\\"expected_result\\\": \\\"提示手机号码格式错误\\\", \\\"actual_result\\\": \\\"\\\", \\\"status\\\": \\\"not executed\\\", \\\"priority\\\": \\\"high\\\"}, {\\\"id\\\": \\\"TC003\\\", \\\"title\\\": \\\"验证验证码输入正确\\\", \\\"description\\\": \\\"验证用户输入的验证码是否正确\\\", \\\"steps\\\": [\\\"打开登录页面\\\", \\\"输入有效的手机号码\\\", \\\"点击获取验证码\\\", \\\"输入正确的验证码\\\", \\\"点击登录\\\"], \\\"expected_result\\\": \\\"成功登录\\\", \\\"actual_result\\\": \\\"\\\", \\\"status\\\": \\\"not executed\\\", \\\"priority\\\": \\\"high\\\"}, {\\\"id\\\": \\\"TC004\\\", \\\"title\\\": \\\"验证验证码输入错误\\\", \\\"description\\\": \\\"验证用户输入的验证码错误时的情况\\\", \\\"steps\\\": [\\\"打开登录页面\\\", \\\"输入有效的手机号码\\\", \\\"点击获取验证码\\\", \\\"输入错误的验证码\\\", \\\"点击登录\\\"], \\\"expected_result\\\": \\\"提示验证码错误\\\", \\\"actual_result\\\": \\\"\\\", \\\"status\\\": \\\"not executed\\\", \\\"priority\\\": \\\"high\\\"}, {\\\"id\\\": \\\"TC005\\\", \\\"title\\\": \\\"验证验证码过期\\\", \\\"description\\\": \\\"验证验证码过期后是否还能使用\\\", \\\"steps\\\": [\\\"打开登录页面\\\", \\\"输入有效的手机号码\\\", \\\"点击获取验证码\\\", \\\"等待验证码过期\\\", \\\"输入过期的验证码\\\", \\\"点击登录\\\"], \\\"expected_result\\\": \\\"提示验证码已过期\\\", \\\"actual_result\\\": \\\"\\\", \\\"status\\\": \\\"not executed\\\", \\\"priority\\\": \\\"medium\\\"}, {\\\"id\\\": \\\"TC006\\\", \\\"title\\\": \\\"验证验证码发送频率限制\\\", \\\"description\\\": \\\"验证在一定时间内多次请求验证码的情况\\\", \\\"steps\\\": [\\\"打开登录页面\\\", \\\"输入有效的手机号码\\\", \\\"点击获取验证码\\\", \\\"在短时间内多次点击获取验证码\\\"], \\\"expected_result\\\": \\\"提示验证码发送过于频繁\\\", \\\"actual_result\\\": \\\"\\\", \\\"status\\\": \\\"not executed\\\", \\\"priority\\\": \\\"medium\\\"}, {\\\"id\\\": \\\"TC007\\\", \\\"title\\\": \\\"验证验证码安全性\\\", \\\"description\\\": \\\"验证验证码是否在传输过程中加密\\\", \\\"steps\\\": [\\\"打开登录页面\\\", \\\"输入有效的手机号码\\\", \\\"点击获取验证码\\\", \\\"使用抓包工具查看验证码传输过程\\\"], \\\"expected_result\\\": \\\"验证码在传输过程中加密\\\", \\\"actual_result\\\": \\\"\\\", \\\"status\\\": \\\"not executed\\\", \\\"priority\\\": \\\"high\\\"}, {\\\"id\\\": \\\"TC008\\\", \\\"title\\\": \\\"验证登录性能\\\", \\\"description\\\": \\\"验证登录过程的响应时间\\\", \\\"steps\\\": [\\\"打开登录页面\\\", \\\"输入有效的手机号码\\\", \\\"点击获取验证码\\\", \\\"输入正确的验证码\\\", \\\"点击登录\\\", \\\"记录登录响应时间\\\"], \\\"expected_result\\\": \\\"登录响应时间在可接受范围内\\\", \\\"actual_result\\\": \\\"\\\", \\\"status\\\": \\\"not executed\\\", \\\"priority\\\": \\\"medium\\\"}]\", \"schema_id\": \"006\", \"dataset_id\": \"006\"}}",
  "message_files": []
}
```

当 tool 为 "CREATE_JSON_DATA", "APPEND_JSON_DATA" 换起 Canvas Form, 并解析 tool_input 里面的 data应用到表格里。

对于 "REPLACE_JSON_DATA" 事件, 其 data 为 jsondiff 格式的数据，将其应用到表格。

2. 用户手动修改 Table 如何处理。

允许用户手动修改 Table 里的数据，但前端需要主动将其作为 `final data` 提交至服务器。
同时在新的继续会话中包含 `final data`

3. 数据如何与外部系统对接

ChatBox 组件通过事件将 final data 回调给调用方。

```html
<ChatBox onJsonDataSave=(data)=>{} />
```

## 未来

后续将实现 Canvas Doc 来支持需求文档的生成。