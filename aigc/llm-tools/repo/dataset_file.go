package repo

import (
	"encoding/json"
	"io/ioutil"
	"os"
	"path/filepath"

	jsonpatch "github.com/evanphx/json-patch/v5"
)

// FileDatasetStorage implements the DatasetStorage interface using a file as storage.
type FileDatasetStorage struct {
	basePath string // Directory to store dataset files
}

func NewFileDatasetStorage(basePath string) (*FileDatasetStorage, error) {
	// Create the base directory if it doesn't exist
	err := os.MkdirAll(basePath, 0755) // 0755 is a common permission setting
	if err != nil {
		return nil, err
	}
	return &FileDatasetStorage{basePath: basePath}, nil
}

func (s *FileDatasetStorage) Get(id string) *Dataset {
	filePath := s.getFilePath(id)
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			// Return a new empty dataset if the file doesn't exist
			return &Dataset{Data: make([]any, 0)}
		}
		panic(err) // Handle other errors
	}

	var dataset Dataset
	err = json.Unmarshal(data, &dataset.Data)
	if err != nil {
		panic(err) // Handle JSON unmarshaling errors
	}
	return &dataset
}

func (s *FileDatasetStorage) Save(id string, dataset *Dataset) {
	filePath := s.getFilePath(id)
	data, err := json.Marshal(dataset.Data)
	if err != nil {
		panic(err)
	}
	err = ioutil.WriteFile(filePath, data, 0644) // 0644 is common permission for data files
	if err != nil {
		panic(err)
	}
}

func (s *FileDatasetStorage) Replace(id string, diff any) {
	d := s.Get(id) // Read existing data
	diffBytes, err := json.Marshal(diff)
	if err != nil {
		panic(err)
	}

	dataBytes, err := json.Marshal(d.Data)
	if err != nil {
		panic(err)
	}

	patch, err := jsonpatch.DecodePatch(diffBytes)
	if err != nil {
		panic(err)
	}

	modified, err := patch.Apply(dataBytes)
	if err != nil {
		panic(err)
	}

	var modifiedData []any
	err = json.Unmarshal(modified, &modifiedData)
	if err != nil {
		panic(err)
	}

	d.Data = modifiedData
	s.Save(id, d) // Save the updated data
}

func (s *FileDatasetStorage) Append(id string, data []any) {
	d := s.Get(id)                   // Read existing data
	d.Data = append(data, d.Data...) // Append new data
	s.Save(id, d)                    // Save the updated data
}

func (s *FileDatasetStorage) getFilePath(id string) string {
	return filepath.Join(s.basePath, id+".json")
}
