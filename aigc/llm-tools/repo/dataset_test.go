package repo

import (
	"io/ioutil"
	"os"
	"reflect"
	"testing"
)

func TestDataset_Replace(t *testing.T) {
	tests := []struct {
		name    string
		dataset *Dataset
		diff    any
		want    []any
	}{
		{
			name:    "add element",
			dataset: NewDataset([]any{map[string]any{"a": 1, "b": 2}}),
			diff:    map[string]any{"c": 3},
			want:    []any{map[string]any{"a": 1, "b": 2, "c": 3}},
		},
		{
			name:    "remove element",
			dataset: NewDataset([]any{map[string]any{"a": 1, "b": 2}}),
			diff:    map[string]any{"b": nil},
			want:    []any{map[string]any{"a": 1}},
		},
		{
			name:    "replace element",
			dataset: NewDataset([]any{map[string]any{"a": 1, "b": 2}}),
			diff:    map[string]any{"b": 3},
			want:    []any{map[string]any{"a": 1, "b": 3}},
		},
		{
			name:    "replace array",
			dataset: NewDataset([]any{1, 2, 3}),
			diff:    []any{4, 5, 6},
			want:    []any{4, 5, 6},
		},
		{
			name:    "replace with null",
			dataset: NewDataset([]any{map[string]any{"a": 1}}),
			diff:    map[string]any{"a": nil},
			want:    []any{map[string]any{}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			storage := NewInMemoryDatasetStorage()
			d := tt.dataset
			storage.Save("test_id", d)
			storage.Replace("test_id", tt.diff)
			d = storage.Get("test_id")
			if !reflect.DeepEqual(d.Data, tt.want) {
				t.Errorf("Replace() = %v, want %v", d.Data, tt.want)
			}
		})
	}

	// 测试 panic 情况
	invalidDiffs := []any{
		123, // 非 JSON 对象或数组
		"invalid json",
	}

	for _, invalidDiff := range invalidDiffs {
		t.Run("panic test", func(t *testing.T) {
			defer func() {
				if r := recover(); r == nil {
					t.Errorf("The code did not panic")
				}
			}()
			storage := NewInMemoryDatasetStorage()
			d := NewDataset([]any{1, 2, 3})
			storage.Save("test_id", d)
			storage.Replace("test_id", invalidDiff)
		})
	}
}

func TestFileStorage(t *testing.T) {
	// Create a temporary directory for testing
	tempDir, err := ioutil.TempDir("", "dataset_test")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tempDir) // Clean up after the test

	// Use file storage
	err = UseFileStorage(tempDir)
	if err != nil {
		t.Fatal(err)
	}

	// Get the storage and dataset instances
	storage, getDataset := GetSets()

	// Test Append
	datasetID := "test_file_dataset"
	dataToAppend := []any{1, 2, 3}
	storage.Append(datasetID, dataToAppend)
	dataset := getDataset(datasetID)
	if !reflect.DeepEqual(dataset.Data, dataToAppend) {
		t.Errorf("Append() = %v, want %v", dataset.Data, dataToAppend)
	}

	// Test Replace
	dataToReplace := map[string]any{"a": 1}
	storage.Replace(datasetID, dataToReplace)
	dataset = getDataset(datasetID)

	expectedData := []any{map[string]any{"a": 1}}

	if !reflect.DeepEqual(dataset.Data, expectedData) {
		t.Errorf("Replace() = %v, want %v", dataset.Data, expectedData)
	}

	// Test Get with a different instance (simulating a new request)
	storage2, getDataset2 := GetSets() // GetSets should return the currently configured storage
	dataset2 := getDataset2(datasetID)
	if _, ok := storage2.(*FileDatasetStorage); !ok {
		t.Errorf("GetSets did not return FileDatasetStorage after UseFileStorage")
	}

	if !reflect.DeepEqual(dataset2.Data, expectedData) {
		t.Errorf("Get() after restart = %v, want %v", dataset2.Data, expectedData)
	}
}

func TestDataset_Append(t *testing.T) {
	tests := []struct {
		name    string
		dataset *Dataset
		data    []any
		want    []any
	}{
		{
			name:    "append to non-empty dataset",
			dataset: NewDataset([]any{1, 2}),
			data:    []any{3, 4},
			want:    []any{3, 4, 1, 2},
		},
		{
			name:    "append to empty dataset",
			dataset: NewDataset([]any{}),
			data:    []any{1, 2},
			want:    []any{1, 2},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			storage := NewInMemoryDatasetStorage()
			d := tt.dataset
			storage.Save("test_id", d)
			storage.Append("test_id", tt.data)
			d = storage.Get("test_id")
			if !reflect.DeepEqual(d.Data, tt.want) {
				t.Errorf("Append() = %v, want %v", d.Data, tt.want)
			}
		})
	}
}
