package repo

import (
	"database/sql"
	"fmt"
	"git.zhonganinfo.com/zainfo/shiplib/config/env"
	_ "github.com/lib/pq"
)

var host = env.String()

var GetDB = func() func() *sql.DB {

	const (
		host      = "localhost"
		port      = 5433
		user      = "admin"
		password  = "admin123"
		dbname    = "test_db"
		tablename = "hospital_admission_plans"
	)

	var db *sql.DB
	var err error
	// 连接到PostgreSQL数据库
	psqlInfo := fmt.Sprintf("host=%s port=%d user=%s "+
		"password=%s dbname=%s sslmode=disable",
		host, port, user, password, dbname)
	db, err = sql.Open("postgres", psqlInfo)
	if err != nil {
		panic(err)
	}
	defer db.Close()

	err = db.Ping()
	if err != nil {
		panic(err)
	}
	return func() *sql.DB {
		return db
	}
}()
