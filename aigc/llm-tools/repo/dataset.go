package repo

import (
	"encoding/json"

	jsonpatch "github.com/evanphx/json-patch/v5"
)

// DatasetStorage is the interface for dataset storage.
type DatasetStorage interface {
	Get(id string) *Dataset
	Save(id string, dataset *Dataset)
	Replace(id string, diff any)
	Append(id string, data []any)
}

var defaultStorage, _ = NewFileDatasetStorage(".data")

// UseFileStorage switches to file-based storage.
func UseFileStorage(basePath string) error {
	fileStorage, err := NewFileDatasetStorage(basePath)
	if err != nil {
		return err
	}
	defaultStorage = fileStorage
	return nil
}

// InMemoryDatasetStorage implements the DatasetStorage interface using an in-memory map.
type InMemoryDatasetStorage struct {
	datasets map[string]*Dataset
}

func NewInMemoryDatasetStorage() *InMemoryDatasetStorage {
	return &InMemoryDatasetStorage{datasets: make(map[string]*Dataset)}
}

func (s *InMemoryDatasetStorage) Get(id string) *Dataset {
	if s.datasets[id] == nil {
		s.datasets[id] = &Dataset{Data: make([]any, 0)}
	}
	return s.datasets[id]
}

func (s *InMemoryDatasetStorage) Save(id string, dataset *Dataset) {
	s.datasets[id] = dataset
}

func (s *InMemoryDatasetStorage) Replace(id string, diff any) {
	d := s.Get(id)
	diffBytes, err := json.Marshal(diff)
	if err != nil {
		panic(err) // 应该以更优雅的方式处理错误
	}

	dataBytes, err := json.Marshal(d.Data)
	if err != nil {
		panic(err) // 应该以更优雅的方式处理错误
	}

	modifiedDataBytes, err := jsonpatch.MergePatch(dataBytes, diffBytes)
	if err != nil {
		panic(err) // 应该以更优雅的方式处理错误
	}

	var modifiedData []any
	err = json.Unmarshal(modifiedDataBytes, &modifiedData)
	if err != nil {
		panic(err) // 应该以更优雅的方式处理错误
	}

	d.Data = modifiedData
}

func (s *InMemoryDatasetStorage) Append(id string, data []any) {
	d := s.Get(id)
	d.Data = append(data, d.Data...)
}

var GetSets = func() (DatasetStorage, func(id string) *Dataset) {
	return defaultStorage, func(id string) *Dataset {
		return defaultStorage.Get(id)
	}
}

type Dataset struct {
	// Data 是 json 格式的数组数据
	Data []any
}

func NewDataset(data []any) *Dataset {
	return &Dataset{Data: data}
}
