package main

import (
	http2 "net/http"

	"github.com/headless-go/nextgo-mux"
	"github.com/headless-go/nextgo/http"
	"github.com/headless-go/nextgo/http/codec"

	api "codebase.zhonganinfo.com/zainfo/cube-kits/aigc/llm-tools/http/generated"
)

func main() {
	svc := mux.New()
	api.Handle(svc,
		api.WithOption(
			http.WithCodec(codec.New(codec.WithPathDecode(mux.PathDecode)))))
	http2.ListenAndServe(":8080", svc)
}
