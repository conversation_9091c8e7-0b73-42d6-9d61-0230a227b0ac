package v1

import (
	"encoding/json"
	"errors"
	"fmt"
	http2 "net/http"

	"github.com/headless-go/nextgo"

	"codebase.zhonganinfo.com/zainfo/cube-kits/aigc/llm-tools/repo"
	"codebase.zhonganinfo.com/zainfo/cube-kits/aigc/llm-tools/utils"
)

type ReplaceJSONDataReq struct {
	JsonDiff  string `json:"json_diff"`
	SchemaID  string `json:"schema_id"`
	DatasetID string `json:"dataset_id"`
}

var _ = nextgo.Mapping.HttpMethod(http2.MethodPost)

func ReplaceJsonData(rw http2.ResponseWriter, diff ReplaceJSONDataReq) error {

	fmt.Println(utils.Beautify(diff))
	var list any
	if err := json.Unmarshal([]byte(diff.JsonDiff), &list); err != nil {
		return errors.New("invalid json")
	}
	storage, sets := repo.GetSets()
	storage.Replace(diff.DatasetID, list) // Note: We pass the diff directly
	dataset := sets(diff.DatasetID)
	rw.Write([]byte(utils.Response(dataset.Data)))
	return nil
}
