package v1

import (
	"fmt"
	"net/http"

	"github.com/headless-go/nextgo"
)

type AskQuestionRequest struct {
	Question string `json:"question"`
}

var _ = nextgo.Mapping.HttpMethod(http.MethodPost)

func AskFollowQuestion(rw http.ResponseWriter, r AskQuestionRequest) error {
	rw.Write([]byte(fmt.Sprintf("<ASK_FOLLOW_QUESTION><Question>%s</Question></ASK_FOLLOW_QUESTION>", r.Question)))
	return nil
}
