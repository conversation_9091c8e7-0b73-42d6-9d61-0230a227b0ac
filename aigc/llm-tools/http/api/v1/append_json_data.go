package v1

import (
	"encoding/json"
	"errors"
	"fmt"
	http2 "net/http"

	"github.com/headless-go/nextgo"

	"codebase.zhonganinfo.com/zainfo/cube-kits/aigc/llm-tools/repo"
	"codebase.zhonganinfo.com/zainfo/cube-kits/aigc/llm-tools/utils"
)

type AppendJSONDataReq struct {
	Data      string `json:"data"`
	SchemaID  string `json:"schema_id"`
	DatasetID string `json:"dataset_id"`
}

var _ = nextgo.Mapping.HttpMethod(http2.MethodPost)

func AppendJsonData(rw http2.ResponseWriter, data CreateJSONDataReq) error {
	fmt.Println(utils.Beautify(data))
	var list any
	if err := json.Unmarshal([]byte(data.Data), &list); err != nil {
		return errors.New("invalid json")
	}
	storage, sets := repo.GetSets()
	storage.Append(data.DatasetID, list.([]any))
	dataset := sets(data.DatasetID)
	rw.Write([]byte(utils.Response(dataset.Data)))
	return nil
}
