package database

import (
	"fmt"
	"net/http"

	"codebase.zhonganinfo.com/zainfo/cube-kits/aigc/llm-tools/repo"
)

type QueryReq struct {
	SQL string `json:"sql"`
}

type Response struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type ExecuteResult struct {
	Rows   []map[string]any `json:"rows"`
	Fields []string         `json:"fields"`
}

func ExecuteQuery(w http.ResponseWriter, args QueryReq) (*ExecuteResult, error) {

	//// 为安全起见,总是添加LIMIT子句
	//if !hasLimit(request.SQL) {
	//	request.SQL += " LIMIT 1000"
	//}
	//

	var err error

	fmt.Println(args.SQL)
	rows, err1 := repo.GetDB().Query(args.SQL)
	if err1 != nil {
		return nil, err1
	}
	defer rows.Close()

	columns, err1 := rows.Columns()
	if err1 != nil {
		return nil, err1
	}

	var result []map[string]interface{}
	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range columns {
			valuePtrs[i] = &values[i]
		}

		err = rows.Scan(valuePtrs...)
		if err != nil {
			return nil, err
		}

		row := make(map[string]interface{})
		for i, col := range columns {
			var v interface{}
			val := values[i]
			b, ok := val.([]byte)
			if ok {
				v = string(b)
			} else {
				v = val
			}
			row[col] = v
		}
		result = append(result, row)
	}

	res := ExecuteResult{
		Rows:   result,
		Fields: columns,
	}

	return &res, nil
}
