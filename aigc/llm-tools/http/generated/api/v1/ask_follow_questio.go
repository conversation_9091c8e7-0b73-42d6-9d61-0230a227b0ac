// Code generated by nextgo; DO NOT EDIT.

package v1

import (
	http1 "net/http"

	http2 "github.com/headless-go/nextgo/http"
	"codebase.zhonganinfo.com/zainfo/cube-kits/aigc/llm-tools/http/api/v1"
	"github.com/justinas/alice"
)

func AskFollowQuestionHandleFunc(chain alice.Chain, opt http2.Option) http1.Handler {

	routeInfo := http2.RouteInfo{
		Patten:          "/v1/ask_follow_questio",
		Desc:            "",
		HTTPMethod:      "POST",
		HandlerFuncName: "AskFollowQuestion",
		Request:         []any{v1.AskQuestionRequest{}},
		Middleware:      []string{},
		Label:           map[string]string{},
	}
	opt.AddRoute(routeInfo)

	chain = alice.New(http2.WithRouteInfo(&routeInfo)).Extend(chain)
	handleFunc := func(rw http1.ResponseWriter, req *http1.Request) {

		var r v1.AskQuestionRequest

		if err := opt.Decode(req, &r); err != nil {

			_ = opt.EncodeError(rw, err)
			return
		}
		if err := opt.Struct(r); err != nil {
			_ = opt.EncodeError(rw, err)
			return
		}

		{
			err := v1.AskFollowQuestion(rw, r)
			if err != nil {
				_ = opt.EncodeError(rw, err)
				return
			}

		}
	}
	return chain.ThenFunc(handleFunc)
}
