// Code generated by nextgo; DO NOT EDIT.

package v1

import (
	http1 "net/http"

	http2 "github.com/headless-go/nextgo/http"
	"codebase.zhonganinfo.com/zainfo/cube-kits/aigc/llm-tools/http/api/v1"
	"github.com/justinas/alice"
)

func ReplaceJsonDataHandleFunc(chain alice.Chain, opt http2.Option) http1.Handler {

	routeInfo := http2.RouteInfo{
		Patten:          "/v1/replace_json_data",
		Desc:            "",
		HTTPMethod:      "POST",
		HandlerFuncName: "ReplaceJsonData",
		Request:         []any{v1.ReplaceJSONDataReq{}},
		Middleware:      []string{},
		Label:           map[string]string{},
	}
	opt.AddRoute(routeInfo)

	chain = alice.New(http2.WithRouteInfo(&routeInfo)).Extend(chain)
	handleFunc := func(rw http1.ResponseWriter, req *http1.Request) {

		var diff v1.ReplaceJSONDataReq

		if err := opt.Decode(req, &diff); err != nil {

			_ = opt.EncodeError(rw, err)
			return
		}
		if err := opt.Struct(diff); err != nil {
			_ = opt.EncodeError(rw, err)
			return
		}

		{
			err := v1.ReplaceJsonData(rw, diff)
			if err != nil {
				_ = opt.EncodeError(rw, err)
				return
			}

		}
	}
	return chain.ThenFunc(handleFunc)
}
