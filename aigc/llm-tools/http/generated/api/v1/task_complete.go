// Code generated by nextgo; DO NOT EDIT.

package v1

import (
	http1 "net/http"

	http2 "github.com/headless-go/nextgo/http"
	"codebase.zhonganinfo.com/zainfo/cube-kits/aigc/llm-tools/http/api/v1"
	"github.com/justinas/alice"
)

func TaskCompleteHandleFunc(chain alice.Chain, opt http2.Option) http1.Handler {

	routeInfo := http2.RouteInfo{
		Patten:          "/v1/task_complete",
		Desc:            "",
		HTTPMethod:      "GET",
		HandlerFuncName: "TaskComplete",
		Request:         []any{},
		Middleware:      []string{},
		Label:           map[string]string{},
	}
	opt.AddRoute(routeInfo)

	chain = alice.New(http2.WithRouteInfo(&routeInfo)).Extend(chain)
	handleFunc := func(rw http1.ResponseWriter, req *http1.Request) {

		{
			err := v1.TaskComplete(rw)
			if err != nil {
				_ = opt.EncodeError(rw, err)
				return
			}

		}
	}
	return chain.ThenFunc(handleFunc)
}
