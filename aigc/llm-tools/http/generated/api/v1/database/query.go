// Code generated by nextgo; DO NOT EDIT.

package database

import (
	http1 "net/http"

	http2 "github.com/headless-go/nextgo/http"
	"codebase.zhonganinfo.com/zainfo/cube-kits/aigc/llm-tools/http/api/v1/database"
	"github.com/justinas/alice"
)

func ExecuteQueryHandleFunc(chain alice.Chain, opt http2.Option) http1.Handler {

	routeInfo := http2.RouteInfo{
		Patten:          "/v1/database/query",
		Desc:            "",
		HTTPMethod:      "GET",
		HandlerFuncName: "ExecuteQuery",
		Request:         []any{database.QueryReq{}},
		Middleware:      []string{},
		Label:           map[string]string{},
	}
	opt.AddRoute(routeInfo)

	chain = alice.New(http2.WithRouteInfo(&routeInfo)).Extend(chain)
	handleFunc := func(rw http1.ResponseWriter, req *http1.Request) {

		w := rw

		var args database.QueryReq

		if err := opt.Decode(req, &args); err != nil {

			_ = opt.EncodeError(rw, err)
			return
		}
		if err := opt.Struct(args); err != nil {
			_ = opt.EncodeError(rw, err)
			return
		}

		{
			executeResult, err := database.ExecuteQuery(w, args)
			if err != nil {
				_ = opt.EncodeError(rw, err)
				return
			}

			_ = opt.Encode(rw, executeResult)

		}
	}
	return chain.ThenFunc(handleFunc)
}
