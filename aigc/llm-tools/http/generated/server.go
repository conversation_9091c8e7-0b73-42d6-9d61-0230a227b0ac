// Code generated by nextgo; DO NOT EDIT.

package api

import (
	http3 "net/http"

	http2 "github.com/headless-go/nextgo/http"
	"codebase.zhonganinfo.com/zainfo/cube-kits/aigc/llm-tools/http/generated/api/v1"
	"codebase.zhonganinfo.com/zainfo/cube-kits/aigc/llm-tools/http/generated/api/v1/database"
	"github.com/justinas/alice"
)

type application struct {
	optFunc []http2.OptionFunc
}

type OptionFunc func(app *application)

func WithMiddlewares(func(http3.Handler) http3.Handler) func(app *application) {
	return func(app *application) {

	}
}

func WithOption(opts ...http2.OptionFunc) func(m *application) {
	return func(app *application) { app.optFunc = opts }
}

func Handle(svr http2.Server, opts ...OptionFunc) {

	app := application{}
	for _, f := range opts {
		f(&app)
	}
	opt := http2.NewDefaultOption(app.optFunc...)

	svr.HandleFunc("POST", "/v1/create_json_data", v1.CreateJsonDataHandleFunc(alice.New(), opt).ServeHTTP)

	svr.HandleFunc("POST", "/v1/replace_json_data", v1.ReplaceJsonDataHandleFunc(alice.New(), opt).ServeHTTP)

	svr.HandleFunc("GET", "/v1/task_complete", v1.TaskCompleteHandleFunc(alice.New(), opt).ServeHTTP)

	svr.HandleFunc("GET", "/v1/database/query", database.ExecuteQueryHandleFunc(alice.New(), opt).ServeHTTP)

	svr.HandleFunc("POST", "/v1/append_json_data", v1.AppendJsonDataHandleFunc(alice.New(), opt).ServeHTTP)

	svr.HandleFunc("POST", "/v1/ask_follow_questio", v1.AskFollowQuestionHandleFunc(alice.New(), opt).ServeHTTP)

}
