[{"actual_result": "", "description": "验证用户输入的手机号码格式是否正确", "expected_result": "验证码成功发送", "id": "TC001", "priority": "high", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号码", "点击获取验证码"], "title": "验证手机号码格式正确"}, {"actual_result": "", "description": "验证用户输入的手机号码格式错误时的情况", "expected_result": "提示手机号码格式错误", "id": "TC002", "priority": "high", "status": "not executed", "steps": ["打开登录页面", "输入无效的手机号码", "点击获取验证码"], "title": "验证手机号码格式错误"}, {"actual_result": "", "description": "验证用户输入的验证码是否正确", "expected_result": "成功登录", "id": "TC003", "priority": "high", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号码", "点击获取验证码", "输入正确的验证码", "点击登录"], "title": "验证验证码输入正确"}, {"actual_result": "", "description": "验证用户输入的验证码错误时的情况", "expected_result": "提示验证码错误", "id": "TC004", "priority": "high", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号码", "点击获取验证码", "输入错误的验证码", "点击登录"], "title": "验证验证码输入错误"}, {"actual_result": "", "description": "验证验证码过期后是否还能使用", "expected_result": "提示验证码已过期", "id": "TC005", "priority": "medium", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号码", "点击获取验证码", "等待验证码过期", "输入过期的验证码", "点击登录"], "title": "验证验证码过期"}, {"actual_result": "", "description": "验证在一定时间内多次请求验证码的情况", "expected_result": "提示验证码发送过于频繁", "id": "TC006", "priority": "medium", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号码", "点击获取验证码", "在短时间内多次点击获取验证码"], "title": "验证验证码发送频率限制"}, {"actual_result": "", "description": "验证验证码是否在传输过程中加密", "expected_result": "验证码在传输过程中加密", "id": "TC007", "priority": "high", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号码", "点击获取验证码", "使用抓包工具查看验证码传输过程"], "title": "验证验证码安全性"}, {"actual_result": "", "description": "验证登录过程的响应时间", "expected_result": "登录响应时间在可接受范围内", "id": "TC008", "priority": "medium", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号码", "点击获取验证码", "输入正确的验证码", "点击登录", "记录登录响应时间"], "title": "验证登录性能"}]