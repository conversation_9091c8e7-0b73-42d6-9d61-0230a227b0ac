[{"actual_result": "", "description": "验证用户尝试重复使用已经使用过的验证码时系统如何处理", "expected_result": "系统提示验证码已经使用过，登录失败", "id": "TC006", "priority": "medium", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号码", "点击获取验证码按钮", "输入收到的验证码", "点击登录按钮", "重新输入同一个验证码", "点击登录按钮"], "title": "验证码重复使用测试"}, {"actual_result": "", "description": "验证用户输入手机号后尝试连续获取验证码时系统如何处理", "expected_result": "系统提示验证码发送频率过高，请稍后再试", "id": "TC007", "priority": "medium", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号码", "点击获取验证码按钮", "等待几秒", "再次点击获取验证码按钮"], "title": "验证码发送频率限制测试"}, {"actual_result": "", "description": "验证系统在发送验证码时出现错误时如何处理", "expected_result": "系统提示验证码发送失败，请重试", "id": "TC008", "priority": "medium", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号码", "点击获取验证码按钮"], "title": "验证码错误输入测试"}, {"actual_result": "", "description": "验证在发送验证码过程中网络中断时系统如何处理", "expected_result": "系统提示网络中断，验证码发送失败", "id": "TC009", "priority": "medium", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号码", "点击获取验证码按钮", "在发送过程中中断网络"], "title": "验证码发送过程中网络中断测试"}, {"actual_result": "", "description": "验证在发送验证码过程中系统崩溃时如何处理", "expected_result": "系统提示系统崩溃，验证码发送失败", "id": "TC010", "priority": "medium", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号码", "点击获取验证码按钮", "在发送过程中系统崩溃"], "title": "验证码发送过程中系统崩溃测试"}, {"actual_result": "", "description": "验证在用户输入手机号后，系统能否正确发送验证码", "expected_result": "系统成功发送验证码到指定手机号", "id": "TC001", "priority": "high", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号码", "点击获取验证码按钮"], "title": "验证码发送功能测试"}, {"actual_result": "", "description": "验证用户输入正确的验证码后能否成功登录", "expected_result": "用户成功登录系统", "id": "TC002", "priority": "high", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号码", "点击获取验证码按钮", "输入收到的验证码", "点击登录按钮"], "title": "验证码输入功能测试"}, {"actual_result": "", "description": "验证用户输入错误的验证码时系统如何处理", "expected_result": "系统提示验证码错误，登录失败", "id": "TC003", "priority": "medium", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号码", "点击获取验证码按钮", "输入错误的验证码", "点击登录按钮"], "title": "验证码错误输入测试"}, {"actual_result": "", "description": "验证验证码过期后用户输入验证码时系统如何处理", "expected_result": "系统提示验证码已过期，登录失败", "id": "TC004", "priority": "medium", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号码", "点击获取验证码按钮", "等待验证码过期", "输入过期的验证码", "点击登录按钮"], "title": "验证码过期测试"}, {"actual_result": "", "description": "验证用户点击重发验证码按钮后系统能否重新发送验证码", "expected_result": "系统重新发送验证码到指定手机号", "id": "TC005", "priority": "medium", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号码", "点击获取验证码按钮", "点击重发验证码按钮"], "title": "验证码重发功能测试"}]