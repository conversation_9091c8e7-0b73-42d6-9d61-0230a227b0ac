[{"actual_result": "", "description": "验证在规定时间内重复获取验证码是否被限制", "expected_result": "提示操作过于频繁，请稍后再试", "id": "TC014", "priority": "medium", "status": "not executed", "steps": ["进入登录页面", "输入符合格式的手机号", "点击获取验证码", "立即再次点击获取验证码"], "title": "Verify the frequency limit of verification code sending"}, {"actual_result": "", "description": "验证验证码是否存在缓存漏洞", "expected_result": "验证码未被缓存，无法获取", "id": "TC015", "priority": "medium", "status": "not executed", "steps": ["进入登录页面", "输入符合格式的手机号", "点击获取验证码", "使用缓存工具获取验证码"], "title": "验证验证码缓存漏洞"}, {"actual_result": "", "description": "验证用户输入符合手机号格式要求", "expected_result": "验证码发送成功", "id": "TC001", "priority": "medium", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号", "点击获取验证码"], "title": "验证手机号格式正确性"}, {"actual_result": "", "description": "验证验证码在有效期内可成功登录", "expected_result": "登录成功", "id": "TC002", "priority": "medium", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号", "获取验证码", "在有效期内输入验证码", "点击登录"], "title": "验证验证码有效期"}, {"actual_result": "", "description": "验证验证码重发功能是否正常", "expected_result": "收到新的验证码", "id": "TC003", "priority": "medium", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号", "获取验证码", "点击重发验证码"], "title": "验证验证码重发机制"}, {"actual_result": "", "description": "验证登录失败次数限制功能", "expected_result": "达到限制次数后账户被锁定", "id": "TC004", "priority": "medium", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号", "输入错误的验证码", "重复登录尝试"], "title": "验证登录次数限制"}, {"actual_result": "", "description": "验证验证码是否容易被截获或猜测", "expected_result": "验证码无法被猜测或截获", "id": "TC005", "priority": "medium", "status": "not executed", "steps": ["打开登录页面", "输入有效的手机号", "获取验证码", "尝试猜测验证码"], "title": "验证验证码泄露风险"}, {"actual_result": "", "description": "验证验证码是否存在可被暴力破解的漏洞", "expected_result": "系统拒绝常用验证码并提示错误", "id": "TC009", "priority": "high", "status": "not executed", "steps": ["进入登录页面", "输入符合格式的手机号", "点击获取验证码", "尝试使用常用的验证码进行破解"], "title": "验证验证码漏洞测试"}, {"actual_result": "", "description": "验证在高并发情况下验证码发送的性能", "expected_result": "验证码发送成功，系统响应速度在可接受范围内", "id": "TC010", "priority": "medium", "status": "not executed", "steps": ["开启多个浏览器实例", "同时进入登录页面", "同时输入符合格式的手机号", "同时点击获取验证码"], "title": "验证验证码发送性能测试"}, {"actual_result": "", "description": "验证验证码是否存在缓存漏洞", "expected_result": "验证码未被缓存，无法获取", "id": "TC011", "priority": "high", "status": "not executed", "steps": ["进入登录页面", "输入符合格式的手机号", "点击获取验证码", "使用缓存工具获取验证码"], "title": "验证验证码缓存测试"}, {"actual_result": "", "description": "验证验证码在过期后是否无效", "expected_result": "提示验证码已过期，请重新获取", "id": "TC012", "priority": "medium", "status": "not executed", "steps": ["进入登录页面", "输入符合格式的手机号", "点击获取验证码", "等待验证码过期", "输入过期的验证码", "点击登录"], "title": "验证验证码过期时间测试"}, {"actual_result": "", "description": "验证已经使用过的验证码是否可重复使用", "expected_result": "提示验证码已过期，请重新获取", "id": "TC013", "priority": "medium", "status": "not executed", "steps": ["进入登录页面", "输入符合格式的手机号", "点击获取验证码", "输入收到的验证码", "点击登录", "重新进入登录页面", "输入符合格式的手机号", "输入上次收到的验证码", "点击登录"], "title": "验证验证码重复使用测试"}, {"actual_result": "", "description": "验证输入符合格式要求的手机号是否被接受", "expected_result": "成功发送验证码", "id": "TC001", "priority": "high", "status": "not executed", "steps": ["进入登录页面", "输入符合格式的手机号", "点击获取验证码"], "title": "验证手机号格式正确性"}, {"actual_result": "", "description": "验证输入不符合格式的手机号是否被拒绝", "expected_result": "提示手机号格式错误", "id": "TC002", "priority": "high", "status": "not executed", "steps": ["进入登录页面", "输入不符合格式的手机号", "点击获取验证码"], "title": "验证手机号格式错误处理"}, {"actual_result": "", "description": "验证在规定时间内重复获取验证码是否被限制", "expected_result": "提示操作过于频繁，请稍后再试", "id": "TC003", "priority": "medium", "status": "not executed", "steps": ["进入登录页面", "输入符合格式的手机号", "点击获取验证码", "立即再次点击获取验证码"], "title": "验证验证码发送频率限制"}, {"actual_result": "", "description": "验证输入正确的验证码是否成功登录", "expected_result": "成功登录系统", "id": "TC004", "priority": "high", "status": "not executed", "steps": ["进入登录页面", "输入符合格式的手机号", "点击获取验证码", "输入收到的验证码", "点击登录"], "title": "验证验证码输入正确性"}, {"actual_result": "", "description": "验证输入错误的验证码是否被拒绝", "expected_result": "提示验证码错误", "id": "TC005", "priority": "high", "status": "not executed", "steps": ["进入登录页面", "输入符合格式的手机号", "点击获取验证码", "输入错误的验证码", "点击登录"], "title": "验证验证码输入错误处理"}, {"actual_result": "", "description": "验证输入过期的验证码是否被拒绝", "expected_result": "提示验证码已过期，请重新获取", "id": "TC006", "priority": "medium", "status": "not executed", "steps": ["进入登录页面", "输入符合格式的手机号", "点击获取验证码", "等待验证码过期", "输入过期的验证码", "点击登录"], "title": "验证验证码过期处理"}, {"actual_result": "", "description": "验证连续多次登录失败是否触发限制", "expected_result": "提示登录次数过多，请稍后再试", "id": "TC007", "priority": "medium", "status": "not executed", "steps": ["进入登录页面", "输入符合格式的手机号", "点击获取验证码", "连续多次输入错误验证码并尝试登录"], "title": "验证登录次数限制"}, {"actual_result": "", "description": "验证点击重发验证码是否能成功收到新验证码", "expected_result": "成功收到新验证码", "id": "TC008", "priority": "medium", "status": "not executed", "steps": ["进入登录页面", "输入符合格式的手机号", "点击获取验证码", "点击重发验证码"], "title": "验证验证码重发功能"}]