basePath: /
definitions:
  entity.Project:
    properties:
      id:
        type: integer
      jira_count:
        type: integer
      jira_project_id:
        type: integer
      magic_count:
        type: integer
      magic_library_id:
        type: integer
      magic_project_id:
        type: integer
      name:
        type: string
      owner:
        type: string
    type: object
  server.SyncReq:
    properties:
      jira_project_id:
        type: integer
      magic_library_id:
        type: integer
    type: object
host: 127.0.0.1:8080
info:
  contact: {}
  description: API文档
  title: API 文档
  version: "1.0"
paths:
  /ping:
    get:
      consumes:
      - application/json
      description: 检查服务是否正常运行
      produces:
      - application/json
      responses:
        "200":
          description: '{''msg'':''pong''}'
          schema:
            additionalProperties:
              type: string
            type: object
      summary: 服务健康检查
      tags:
      - 系统x
  /project/list:
    get:
      consumes:
      - application/json
      description: 获取所有项目信息，包括同步状态
      produces:
      - application/json
      responses:
        "200":
          description: 项目列表
          schema:
            items:
              $ref: '#/definitions/entity.Project'
            type: array
      summary: 获取项目列表
      tags:
      - project
  /project/sync:
    post:
      consumes:
      - application/json
      description: 将选中的项目从Jira同步到Magic平台
      parameters:
      - description: 项目ID列表
        in: body
        name: projectIds
        required: true
        schema:
          items:
            $ref: '#/definitions/server.SyncReq'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: 同步后的项目信息
          schema:
            items:
              $ref: '#/definitions/entity.Project'
            type: array
        "500":
          description: 错误信息
          schema:
            type: string
      summary: 同步项目
      tags:
      - project
swagger: "2.0"
