{"swagger": "2.0", "info": {"description": "API文档", "title": "API 文档", "contact": {}, "version": "1.0"}, "host": "127.0.0.1:8080", "basePath": "/", "paths": {"/ping": {"get": {"description": "检查服务是否正常运行", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统x"], "summary": "服务健康检查", "responses": {"200": {"description": "{'msg':'pong'}", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/project/list": {"get": {"description": "获取所有项目信息，包括同步状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["project"], "summary": "获取项目列表", "responses": {"200": {"description": "项目列表", "schema": {"type": "array", "items": {"$ref": "#/definitions/entity.Project"}}}}}}, "/project/sync": {"post": {"description": "将选中的项目从Jira同步到Magic平台", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["project"], "summary": "同步项目", "parameters": [{"description": "项目ID列表", "name": "projectIds", "in": "body", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/server.SyncReq"}}}], "responses": {"200": {"description": "同步后的项目信息", "schema": {"type": "array", "items": {"$ref": "#/definitions/entity.Project"}}}, "500": {"description": "错误信息", "schema": {"type": "string"}}}}}}, "definitions": {"entity.Project": {"type": "object", "properties": {"id": {"type": "integer"}, "jira_count": {"type": "integer"}, "jira_project_id": {"type": "integer"}, "magic_count": {"type": "integer"}, "magic_library_id": {"type": "integer"}, "magic_project_id": {"type": "integer"}, "name": {"type": "string"}, "owner": {"type": "string"}}}, "server.SyncReq": {"type": "object", "properties": {"jira_project_id": {"type": "integer"}, "magic_library_id": {"type": "integer"}}}}}