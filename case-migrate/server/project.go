package server

import (
	"JiraMigrate/configs"
	"JiraMigrate/db"
	"JiraMigrate/entity"
	"encoding/json"
	"log"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

type ProjectServer struct {
}

// 异常处理
func errorHandler(c *gin.Context, err error) {
	c.JSON(500, gin.H{
		"msg": err.Error(),
	})
}

// GetProjects 获取所有的项目信息
//
//	@Summary		获取项目列表
//	@Description	获取所有项目信息，包括同步状态
//	@Tags			project
//	@Accept			json
//	@Produce		json
//	@Success		200	{array}	entity.Project	"项目列表"
//	@Router			/project/list [get]
func (p *ProjectServer) GetProjects(c *gin.Context) {
	c.JSON(200, gin.H{"config": DoGetProject()})
}

func DoGetProject() []entity.Project {
	// Try to read the latest json file from sync-record directory
	files, err := os.ReadDir("sync-record")
	if err != nil || len(files) == 0 {
		log.Printf("no projects found in sync-record directory")
		return getProjectsWithCount(configs.Config.Projects)
	}

	// Find the latest json file
	var latestFile string
	var latestTime time.Time
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".json") {
			fileInfo, err := file.Info()
			if err != nil {
				continue
			}
			if fileInfo.ModTime().After(latestTime) {
				latestTime = fileInfo.ModTime()
				latestFile = file.Name()
			}
		}
	}

	if latestFile == "" {
		// If no json files found, return projects from config
		log.Printf("no projects found in sync-record directory")
		return getProjectsWithCount(configs.Config.Projects)
	}

	// Read and parse the latest json file
	data, err := os.ReadFile(filepath.Join("sync-record", latestFile))
	if err != nil {
		log.Printf("failed to read sync-record file: %v", err)
		return getProjectsWithCount(configs.Config.Projects)
	}

	var projects []entity.Project
	if err := json.Unmarshal(data, &projects); err != nil {
		log.Printf("failed to unmarshal sync-record file: %v", err)
		return getProjectsWithCount(configs.Config.Projects)
	}
	return getProjectsWithCount(projects)
}

// getProjectsWithCount 从数据库获取项目的 Jira 和 Magic 数量
func getProjectsWithCount(projects []entity.Project) []entity.Project {
	jiraDb, err := db.GetDB(configs.Config.Jira.Name)
	if err != nil {
		log.Printf("failed to get jira db: %v", err)
		return projects
	}

	magicDb, err := db.GetDB(configs.Config.Magic.Name)
	if err != nil {
		log.Printf("failed to get magic db: %v", err)
		return projects
	}

	for i := range projects {
		// 获取 Jira 用例数量
		var jiraCount int64
		err := jiraDb.Table("jiraissue").
			Where("project = ? AND issuetype = 10412", projects[i].JiraProjectId).
			Count(&jiraCount).Error
		if err != nil {
			log.Printf("failed to get jira count for project %d: %v", projects[i].JiraProjectId, err)
			continue
		}
		projects[i].JiraCount = int(jiraCount)

		// 获取 Magic 用例数量
		var magicCount int64
		err = magicDb.Table("mars_case").
			Where("library_id = ? AND is_deleted = 'N'", projects[i].MagicLibraryId).
			Count(&magicCount).Error
		if err != nil {
			log.Printf("failed to get magic count for project %d: %v", projects[i].MagicProjectId, err)
			continue
		}
		projects[i].MagicCount = int(magicCount)
	}

	return projects
}

type SyncReq struct {
	JiraPid        int64 `json:"jira_project_id"`
	MagicLibraryId int64 `json:"magic_library_id"`
}

// SyncProjects 同步项目信息,多协程处理
//
//	@Summary		同步项目
//	@Description	将选中的项目从Jira同步到Magic平台
//	@Tags			project
//	@Accept			json
//	@Produce		json
//	@Param			projectIds	body		[]SyncReq			true	"项目ID列表"
//	@Success		200			{array}		entity.Project	"同步后的项目信息"
//	@Failure		500			{object}	string			"错误信息"
//	@Router			/project/sync [post]
func (p *ProjectServer) SyncProjects(c *gin.Context) {
	var pIds []SyncReq
	if err := c.ShouldBindJSON(&pIds); err != nil {
		errorHandler(c, err)
		return
	}
	log.Printf("sync project: %+v", pIds)
	var wg sync.WaitGroup
	resultChan := make(chan struct {
		id      int64
		err     error
		project *entity.Project
	}, len(pIds))

	for _, pId := range pIds {
		wg.Add(1)
		go func(id SyncReq) {
			defer wg.Done()
			err, project := syncProject(id)
			resultChan <- struct {
				id      int64
				err     error
				project *entity.Project
			}{id.JiraPid, err, project}
		}(pId)
	}

	// 启动一个协程等待所有任务完成后关闭channel
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	projectMap := make(map[string]entity.Project)
	for result := range resultChan {
		if result.err != nil {
			log.Printf("failed to sync project %d: %v", result.id, result.err)
			continue
		}
		if result.project != nil {
			projectMap[strconv.FormatInt(result.id, 10)] = *result.project
		}
	}
	writeProjects(c, projectMap)
}

// 写入最新数据到文件
func writeProjects(c *gin.Context, newProjectsMap map[string]entity.Project) {
	// Read the latest file from sync-record directory
	files, err := os.ReadDir("sync-record")
	if err != nil {
		if os.IsNotExist(err) {
			if err := os.MkdirAll("sync-record", 0755); err != nil {
				errorHandler(c, err)
				return
			}
		} else {
			errorHandler(c, err)
			return
		}
	}

	// Find the latest json file
	var latestFile string
	var latestTime time.Time
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".json") {
			fileInfo, err := file.Info()
			if err != nil {
				continue
			}
			if fileInfo.ModTime().After(latestTime) {
				latestTime = fileInfo.ModTime()
				latestFile = file.Name()
			}
		}
	}

	// Initialize projects from the latest file or config
	var projects []entity.Project
	if latestFile != "" {
		// Read and parse the latest json file
		data, err := os.ReadFile(filepath.Join("sync-record", latestFile))
		if err != nil {
			errorHandler(c, err)
			return
		}

		if err := json.Unmarshal(data, &projects); err != nil {
			errorHandler(c, err)
			return
		}
	} else {
		// If no json files found, use projects from config
		projects = configs.Config.Projects
	}

	// Update projects based on newProjectsMap
	for i := range projects {
		if newProject, exists := newProjectsMap[strconv.Itoa(int(projects[i].JiraProjectId))]; exists {
			projects[i] = newProject
		}
	}

	// Generate new JSON file with timestamp
	timestamp := time.Now().Format("20060102-150405")
	filename := filepath.Join("sync-record", timestamp+".json")

	// Marshal projects to JSON
	data, err := json.MarshalIndent(projects, "", "    ")
	if err != nil {
		errorHandler(c, err)
		return
	}

	// Write to file
	if err := os.WriteFile(filename, data, 0644); err != nil {
		errorHandler(c, err)
		return
	}

	c.JSON(200, projects)
}
