package server

import (
	"JiraMigrate/configs"
	"JiraMigrate/contants"
	"JiraMigrate/db"
	"JiraMigrate/entity"
	"JiraMigrate/entity/jira"
	"JiraMigrate/entity/magic"
	"encoding/json"
	"gorm.io/gorm"
	"log"
)

// 同步项目，真正执行同步逻辑
func syncProject(req SyncReq) (error, *entity.Project) {
	log.Printf("start ===========> sync project :%+v", req)
	jiraDb, err := db.GetDB(configs.Config.Jira.Name)
	if err != nil {
		return err, nil
	}
	magicDb, err := db.GetDB(configs.Config.Magic.Name)
	if err != nil {
		return err, nil
	}
	go deleteCaseLibrary(magicDb, req.MagicLibraryId)
	// 1: 创建 magic 用例库
	var pr entity.Project
	for _, p := range configs.Config.Projects {
		if p.JiraProjectId == req.JiraPid {
			marshal, _ := json.Marshal(p)
			json.Unmarshal(marshal, &pr)
		}
	}
	obj := syncJira{
		pId:     req.JiraPid,
		jiraDb:  jiraDb,
		magicDb: magicDb,
		pr:      &pr,
	}
	log.Printf("start 01 ===========> sync case library : jira id=%d", req.JiraPid)
	err = obj.CreateCaseLibrary()
	if err != nil {
		return err, nil
	}
	log.Printf("start 02 ===========> sync case : jira id=%d", req.JiraPid)
	err, jiraMagicMap := obj.CreateCase()
	if err != nil {
		return err, nil
	}
	log.Printf("start 03 ===========> sync step : jira id=%d", req.JiraPid)
	err = obj.CreateCaseStep(jiraMagicMap)
	if err != nil {
		return err, nil
	}
	log.Printf("env ===========> sync project info :%+v", pr)
	return nil, &pr
}

// 删除库下所有数据
func deleteCaseLibrary(db *gorm.DB, libId int64) {
	if libId == 0 {
		return
	}
	// 删除用例库
	log.Printf("delete case library libId=%d", libId)
	db.Delete(&magic.CaseLibrary{}, libId)
	// 删除 node 节点
	log.Printf("delete node hierarchy libId=%d", libId)
	db.Where("library_id = ?", libId).Delete(&magic.MarsNodesHierarchy{})
	var caseList []magic.MarsCase
	db.Select("id").Where("library_id = ?", libId).Find(&caseList)
	var ids []int64
	for _, c := range caseList {
		ids = append(ids, c.ID)
	}
	log.Printf("delete step ,libId=%d", libId)
	// 删除步骤
	db.Where("case_id in ?", ids).Delete(&magic.MarsCaseStep{})
	// 删除 case
	log.Printf("delete case ,libId=%d", libId)
	db.Where("library_id = ?", libId).Delete(&magic.MarsCase{})
}

type syncJira struct {
	pId     int64
	jiraDb  *gorm.DB
	magicDb *gorm.DB
	pr      *entity.Project
}

// CreateCaseLibrary 创建用例库
func (s *syncJira) CreateCaseLibrary() error {
	library := magic.CaseLibrary{
		Name:        s.pr.Name,
		SpaceId:     s.pr.MagicProjectId,
		Share:       0,
		Description: s.pr.Name,
	}
	library.WithInit(s.pr.Owner)
	err := s.magicDb.Create(&library).Error
	if err != nil {
		return err
	}
	s.pr.MagicLibraryId = library.ID
	return nil
}

func (s *syncJira) CreateCase() (error, map[int64]int64) {
	// 0 : 创建 mars_nodes_hierarchy 的用例库根节点
	magicNodesHierarchyRoot := magic.MarsNodesHierarchy{
		LibraryId:  s.pr.MagicLibraryId,
		NodeName:   s.pr.Name,
		ParentId:   0,
		RelationId: nil,
		Type:       1,
	}
	magicNodesHierarchyRoot.WithInit(s.pr.Owner)
	err2 := s.magicDb.Create(&magicNodesHierarchyRoot).Error
	if err2 != nil {
		log.Printf("failed to create magic nodes hierarchy root: %v", err2)
		return err2, nil
	}
	// 1: 获取 jira 所有的用例集
	sql := "select t1.id as id, t1.name as name, t1.project_id as project_id,t2.test_suite_id as parent_id from (" +
		"select * from ao_69e499_testsuite where PROJECT_ID =? ) t1 " +
		"left join ao_69e499_testsuitemember t2 on t1.id=t2.MEMBER_ID order by id"
	var suites []jira.Suite
	err := s.jiraDb.Raw(sql, s.pId).Scan(&suites).Error
	if err != nil {
		log.Printf("failed to get jira suite: %v", err)
		return err, nil
	}
	if len(suites) == 0 {
		log.Printf("project id=%d 没有用例集！", s.pId)
		return nil, nil
	}
	suiteIdMap := make(map[int64]jira.Suite)
	for _, suite := range suites {
		suiteIdMap[suite.Id] = suite
	}
	// jira 用例集的 id 与 magic node id 映射
	suiteMap := make(map[int64]int64)
	suiteMap[0] = magicNodesHierarchyRoot.ID
	for _, suite := range suites {
		err := doNode(s, suiteMap, suiteIdMap, suite)
		if err != nil {
			return err, nil
		}
	}
	// 获取所有的 jira 用例节点
	sqlCase := "select t1.id as id,t1.project as project,t1.issuetype as issuetype,t1.summary as summary,t1.description as description," +
		"t1.priority as priority,t1.issuestatus as issuestatus,t2.test_suite_id as parent_id from " +
		"(select * from `jiraissue` where issuetype = 10412 and project= ? ) t1 " +
		"left join ao_69e499_testsuitemember t2 on t1.id=t2.MEMBER_ID order by parent_id"
	var jiraCases []jira.Case
	err = s.jiraDb.Raw(sqlCase, s.pId).Scan(&jiraCases).Error
	if err != nil {
		log.Printf("failed to get jira case list: %v", err)
		return err, nil
	}
	// 组装成 magic 的用例，并插入 mars_case 表中
	magicCaseList := make([]magic.MarsCase, len(jiraCases))
	for i, jiraCase := range jiraCases {
		c := magic.MarsCase{
			Name:         jiraCase.Summary,
			Priority:     contants.Priority2magic(jiraCase.Priority),
			Review:       contants.ReviewStatus2magic(jiraCase.IssueStatus),
			Sort:         i,
			ParentNodeID: suiteMap[jiraCase.ParentId],
			JiraCaseId:   jiraCase.Id,
			LibraryId:    s.pr.MagicLibraryId,
		}
		c.WithInit(s.pr.Owner)
		magicCaseList[i] = c
	}
	err2 = s.magicDb.CreateInBatches(&magicCaseList, 100).Error
	if err2 != nil {
		log.Printf("failed to create magic case list: %v", err2)
		return err2, nil
	}
	// 将 magic 用例插入到 mars_nodes_hierarchy 表中
	caseNode := make([]magic.MarsNodesHierarchy, len(magicCaseList))
	// jira case id -> magic case id
	jiraMagicMap := make(map[int64]int64)
	for i, c := range magicCaseList {
		node := magic.MarsNodesHierarchy{
			LibraryId:  s.pr.MagicLibraryId,
			NodeName:   c.Name,
			ParentId:   c.ParentNodeID,
			RelationId: &c.ID,
			Type:       6,
			Sort:       c.Sort,
		}
		jiraMagicMap[c.JiraCaseId] = c.ID
		node.WithInit(s.pr.Owner)
		caseNode[i] = node
	}
	err = s.magicDb.CreateInBatches(&caseNode, 100).Error
	if err != nil {
		log.Printf("failed to create magic case nodes hierarchy: %v", err)
		return err, nil
	}
	s.pr.MagicCount = len(magicCaseList)
	s.pr.JiraCount = len(jiraCases)
	return nil, jiraMagicMap
}

func doNode(s *syncJira, suiteMap map[int64]int64, suiteIdMap map[int64]jira.Suite, suite jira.Suite) error {
	_, ok := suiteMap[suite.Id]
	if ok {
		// 已经插入过了
		return nil
	}
	pid, ok := suiteMap[suite.ParentId]
	if !ok {
		log.Printf("suit id=%d 还未在 magic 中插入！调用处理 doNode", suite.ParentId)
		err := doNode(s, suiteMap, suiteIdMap, suiteIdMap[suite.ParentId])
		if err != nil {
			return err
		}
	}
	node := magic.MarsNodesHierarchy{
		LibraryId:  s.pr.MagicLibraryId,
		NodeName:   suite.Name,
		ParentId:   pid,
		RelationId: nil,
		Type:       3,
		Sort:       suite.Sequence,
	}
	if node.ParentId == 0 {
		node.ParentId = suiteMap[0]
	}
	node.WithInit(s.pr.Owner)
	err := s.magicDb.Create(&node).Error
	if err != nil {
		log.Printf("failed to create magic suite nodes hierarchy: %v", err)
		return err
	}
	suiteMap[suite.Id] = node.ID
	return nil
}

func (s *syncJira) CreateCaseStep(jiraMagicMap map[int64]int64) error {
	jiraCaseIds := make([]int64, 0, len(jiraMagicMap))
	// 提取所有键
	for k := range jiraMagicMap {
		jiraCaseIds = append(jiraCaseIds, k)
	}
	var steps []jira.Step
	err := s.jiraDb.Select("id", "tc_id", "step", "expected_result", "sequence").Where("tc_id in ?", jiraCaseIds).Find(&steps).Error
	if err != nil {
		log.Printf("failed to get jira case steps: %v", err)
		return err
	}
	magicCaseStepList := make([]magic.MarsCaseStep, len(steps))
	for i, step := range steps {
		st := magic.MarsCaseStep{
			CaseID:          jiraMagicMap[step.TcId],
			Description:     step.Step,
			ExpectedResults: step.ExpectedResult,
			Sort:            step.Sequence,
			Type:            1,
		}
		st.WithInit(s.pr.Owner)
		magicCaseStepList[i] = st
	}
	err = s.magicDb.CreateInBatches(&magicCaseStepList, 100).Error
	if err != nil {
		log.Printf("failed to create magic case steps: %v", err)
		return err
	}
	return nil
}
