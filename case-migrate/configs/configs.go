package configs

import (
	"JiraMigrate/entity"
	"github.com/spf13/viper"
)

type Jira struct {
	Url  string `json:"url" yaml:"url"`
	Dsn  string `json:"dsn" yaml:"dsn"`
	Name string `json:"name" yaml:"name"`
}

type Magic struct {
	Url  string `json:"url" yaml:"url"`
	Dsn  string `json:"dsn" yaml:"dsn"`
	Name string `json:"name" yaml:"name"`
}

type Configs struct {
	Jira     Jira             `json:"jira" yaml:"jira"`
	Magic    Magic            `json:"magic" yaml:"magic"`
	Projects []entity.Project `json:"projects" yaml:"projects"`
}

var Config *Configs

func Init() {
	viper.SetConfigFile("config.yml")

	// 尝试进行配置读取
	if err := viper.ReadInConfig(); err != nil {
		panic(err)
	}
	err := viper.Unmarshal(&Config)
	if err != nil {
		panic(err)
	}
}
