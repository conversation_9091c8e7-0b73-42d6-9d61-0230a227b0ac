<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>用例迁移</title>
    <style>
        :root {
            --primary-color: #006afff1;
            --background-color: #f5f8fa;
            --text-primary: #5f6368;
            --text-secondary: #697386;
            --border-color: #e5e9f2;
            --gradient-start: #ffffff;
            --gradient-end: #f8fafc;
            --success-color: #10b981;
            --error-color: #f87171;
            --warning-color: #eab308;
        }

        /* 基础样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: var(--background-color);
            color: var(--text-primary);
        }

        .container { max-width: 1300px; margin: 0 auto; }

        /* 标题样式 */
        .page-header {
            text-align: center;
            margin: 20px 0;
            padding: 20px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            position: relative;
        }

        .title-group {
            display: flex;
            align-items: center;
        }

        .brand {
            color: var(--primary-color);
            font-weight: 700;
            font-size: 30px;
            margin-right: 8px;
        }

        .title {
            color: var(--text-primary);
            font-weight: 500;
            font-size: 28px;
        }

        .copyright {
            color: var(--text-secondary);
            font-size: 13px;
            font-weight: 400;
            opacity: 0.8;
            position: relative;
            padding-left: 20px;
        }

        .copyright::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 1px;
            height: 20px;
            background: var(--border-color);
        }

        .copyright span { color: var(--primary-color);}

        /* 统计概览样式 */
        .stats-overview {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-bottom: 24px;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .stats-overview .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
            position: relative;
            padding: 0;
            background: none;
            border: none;
            box-shadow: none;
        }

        .stats-overview .stat-item:not(:last-child)::after {
            content: '•';
            margin-left: 16px;
            color: var(--border-color);
        }

        .stats-overview .stat-value { font-weight: 500; }
        .stats-overview .stat-value.consistent { color: var(--success-color); }
        .stats-overview .stat-value.inconsistent { color: var(--error-color); }

        /* 项目列表样式 */
        .project-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 24px;
            margin-top: 30px;
        }

        .project-item {
            background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(16, 24, 40, 0.1);
            border: 1px solid var(--border-color);
            position: relative;
            min-height: 130px;
            transition: all 0.3s ease;
        }

        .project-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 24px -10px rgba(16, 24, 40, 0.1);
            border-color: rgba(0, 106, 255, 0.1);
        }

        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .project-name {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: 16px;
        }

        /* 统计项样式 */
        .stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
        }

        .stat-item {
            padding: 12px 16px;
            color: var(--text-secondary);
            font-size: 14px;
            position: relative;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--gradient-start);
            transition: all 0.2s ease;
        }

        .stat-item:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0, 106, 255, 0.1);
            background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
        }

        .stat-item a {
            position: absolute;
            inset: 0;
            color: inherit;
            text-decoration: none;
        }

        .stat-item .content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            pointer-events: none;
        }

        .stat-item .number {
            color: var(--primary-color);
            font-weight: 600;
            min-width: 50px;
            text-align: right;
        }

        /* 进度条样式 */
        .progress-bar {
            height: 6px;
            background-color: rgba(234, 179, 8, 0.1);
            border-radius: 3px;
            margin-bottom: 8px;
            overflow: hidden;
            display: none;
        }

        .progress {
            width: 0%;
            height: 100%;
            background: linear-gradient(90deg, var(--warning-color) 0%, #fbbf24 100%);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .progress.success { background: linear-gradient(90deg, var(--success-color) 0%, #059669 100%); }
        .progress.error { background: linear-gradient(90deg, var(--error-color) 0%, #dc2626 100%); }

        /* 状态样式 */
        .status {
            font-size: 12px;
            font-weight: 500;
            color: var(--text-secondary);
            display: none;
            position: absolute;
            bottom: 10px;
            left: 24px;
        }

        .status.migrating {
            color: var(--warning-color);
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status.success { color: var(--success-color); }
        .status.error { color: var(--error-color); }

        /* 状态标签样式 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            gap: 4px;
            position: absolute;
            bottom: 10px;
            right: 24px;
        }

        .status-badge.consistent {
            color: var(--success-color);
            background-color: #e6f8f3;
        }

        .status-badge.inconsistent {
            color: var(--error-color);
            background-color: #fff5f5;
        }

        /* 按钮样式 */
        .button {
            min-width: 50px;
            height: 30px;
            color: #fff;
            display: flex;
            padding: 0 8px;
            justify-content: center;
            align-items: center;
            gap: 6px;
            border-radius: 5px;
            background: #2979ff;
            border: none;
            outline: none;
            cursor: pointer;
            font-weight: 500;
            font-size: 13px;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 106, 255, 0.1);
        }

        .button:hover {
            background: #1e6be6;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 106, 255, 0.2);
        }

        .button:disabled {
            background: #e5e9f2;
            color: #697386;
            cursor: not-allowed;
            transform: none;
        }

        .button i {
            font-size: 13px;
        }

        /* 动画效果 */
        @keyframes migrating-dots {
            50% { transform: translateY(-4px); background-color: #fbbf24; }
        }

        .migrating-dots {
            display: inline-flex;
            gap: 2px;
            margin-left: 2px;
        }

        .migrating-dots span {
            width: 2px;
            height: 2px;
            background-color: var(--warning-color);
            border-radius: 50%;
            animation: migrating-dots 1.4s infinite;
        }

        .migrating-dots span:nth-child(2) { animation-delay: 0.2s; }
        .migrating-dots span:nth-child(3) { animation-delay: 0.4s; }

        .migrating-text {
            background: linear-gradient(90deg, var(--warning-color), #fbbf24, var(--warning-color));
            background-size: 200% auto;
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: shine 2s linear infinite;
        }

        @keyframes shine {
            to { background-position: 200% center; }
        }

        /* 搜索框样式 */
        .search-container {
            margin: 20px 0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: 10px;
            position: relative;
        }

        .search-input {
            width: 150px;
            height: 30px;
            padding: 0 16px 0 35px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            color: var(--text-primary);
            background: var(--gradient-start);
            transition: all 0.2s ease;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            pointer-events: none;
            font-size: 14px;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 106, 255, 0.1);
            width: 250px;
        }

        .search-input::placeholder {
            color: var(--text-secondary);
            opacity: 0.7;
        }

        /* 错误日志样式 */
        .error-log {
            margin-top: 8px;
            padding: 12px;
            background: rgba(254, 242, 242, 0.6);
            border: 1px solid #fecaca;
            border-radius: 8px;
            font-size: 13px;
            color: var(--error-color);
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 150px;
            overflow-y: auto;
            display: none;
        }
    </style>
    <link rel="stylesheet" href="/static/bootstrap-icons.min.css">
</head>
<body>
    <div class="container">
        <script type="application/json" id="backend-data">
            {{.config}}
        </script>
        <h1 class="page-header">
            <div class="title-group">
                <span class="brand">COMAC</span>
                <span class="title">测试用例迁移</span>
            </div>
            <div class="copyright">@<span>众安科技</span> DevCube</div>
        </h1>
        <div class="stats-overview">
            <div class="stat-item">
                <span class="stat-label">总数</span>
                <span class="stat-value" id="total-projects">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">一致</span>
                <span class="stat-value consistent" id="consistent-projects">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">不一致</span>
                <span class="stat-value inconsistent" id="inconsistent-projects">0</span>
            </div>
        </div>
        <div class="search-container">
            <i class="bi bi-search search-icon"></i>
            <input type="text" class="search-input" placeholder="搜索项目名称..." id="projectSearch" autocomplete="off">
            <button class="button" id="migrateAllButton">
                <i class="bi bi-arrow-clockwise"></i>
                全量迁移
            </button>
        </div>
        <div class="project-list" id="projectList"></div>
    </div>

    <script>
        const state = {
            projects: [],
            jira: {},
            magic: {},
            searchQuery: ''
        };

        const utils = {
            createStatusBadge: (isConsistent) => `
                <span class="status-badge ${isConsistent ? 'consistent' : 'inconsistent'}">
                    <i class="bi bi-${isConsistent ? 'check-circle-fill' : 'exclamation-circle-fill'}"></i>
                    ${isConsistent ? '一致' : '不一致'}
                </span>
            `,

            filterProjects: (query) =>
                !query ? state.projects : state.projects.filter(p => p.name.toLowerCase().includes(query.toLowerCase()))
        };

        const view = {
            createProjectElement: (project) => {
                const isConsistent = project.jira_count === project.magic_count;

                return `
                    <div class="project-item" id="project-${project.id}">
                        <div class="project-header">
                            <div class="project-name">${project.name}</div>
                            <button class="button" onclick="controller.startMigration(${project.jira_project_id})">
                                <i class="bi bi-arrow-clockwise"></i>
                                迁移
                            </button>
                        </div>
                        <div class="stats">
                            <div class="stat-item">
                                <a href="${state.jira.url}" target="_blank"></a>
                                <div class="content">
                                    <span>Jira</span>
                                    <span class="number">${project.jira_count}</span>
                                </div>
                            </div>
                            <div class="stat-item">
                                <a href="${state.magic.url}${project.magic_project_id ? '/' + project.magic_project_id +'/magic/space/'+project.magic_project_id+'/func-case-library': ''}" target="_blank"></a>
                                <div class="content">
                                    <span>Magic</span>
                                    <span class="number">${project.magic_count}</span>
                                </div>
                            </div>
                        </div>
                        <div class="progress-bar" id="progress-${project.id}">
                            <div class="progress"></div>
                        </div>
                        <div class="status-container">
                            <div class="status" id="status-${project.id}"></div>
                            ${utils.createStatusBadge(isConsistent)}
                        </div>
                        <div class="error-log" id="error-log-${project.id}"></div>
                    </div>
                `;
            },

            updateStats: () => {
                const filteredProjects = utils.filterProjects(state.searchQuery);
                const consistentCount = filteredProjects.filter(p => p.jira_count === p.magic_count).length;

                document.getElementById('total-projects').textContent = filteredProjects.length;
                document.getElementById('consistent-projects').textContent = consistentCount;
                document.getElementById('inconsistent-projects').textContent = filteredProjects.length - consistentCount;
            },

            updateProjectUI: (project, elements) => {
                const isConsistent = project.jira_count === project.magic_count;

                // 更新 Jira 数量
                const jiraNumberElement = document.getElementById(`project-${project.id}`).querySelector('.stat-item:nth-child(1) .number');
                if (jiraNumberElement) {
                    jiraNumberElement.textContent = project.jira_count;
                }

                // 更新 Magic 数量
                const magicNumberElement = document.getElementById(`project-${project.id}`).querySelector('.stat-item:nth-child(2) .number');
                if (magicNumberElement) {
                    magicNumberElement.textContent = project.magic_count;
                }

                // 更新状态标签
                const statusBadge = elements.project.querySelector('.status-badge');
                if (statusBadge) {
                    statusBadge.outerHTML = utils.createStatusBadge(isConsistent);
                }
            },

            renderProjects: () => {
                const projectList = document.getElementById('projectList');
                const filteredProjects = utils.filterProjects(state.searchQuery);

                // 如果列表为空，直接渲染所有项目
                if (!projectList.children.length) {
                    projectList.innerHTML = filteredProjects.map(view.createProjectElement).join('');
                } else {
                    // 更新现有项目的数据
                    filteredProjects.forEach(project => {
                        const projectElement = document.getElementById(`project-${project.id}`);
                        if (projectElement) {
                            // 只更新数据，保留进度条和状态
                            view.updateProjectUI(project, {
                                project: projectElement
                            });
                        } else {
                            // 新项目，添加到列表
                            projectList.insertAdjacentHTML('beforeend', view.createProjectElement(project));
                        }
                    });

                    // 移除不在过滤结果中的项目
                    Array.from(projectList.children).forEach(element => {
                        const projectId = element.id.replace('project-', '');
                        if (!filteredProjects.find(p => p.id === projectId)) {
                            element.remove();
                        }
                    });
                }

                view.updateStats();
            }
        };

        const controller = {
            async startMigration(projectId) {
                const numericProjectId = parseInt(projectId);
                const project = state.projects.find(p => p.jira_project_id === numericProjectId);
                if (!project) {
                    throw new Error(`Project not found for ID: ${numericProjectId}`);
                }

                const elements = {
                    project: document.getElementById(`project-${project.id}`),
                    button: document.querySelector(`#project-${project.id} .button`),
                    progress: {
                        bar: document.getElementById(`progress-${project.id}`),
                        element: document.querySelector(`#progress-${project.id} .progress`)
                    },
                    status: document.getElementById(`status-${project.id}`),
                    errorLog: document.getElementById(`error-log-${project.id}`)
                };

                // 禁用当前项目的迁移按钮
                elements.button.disabled = true;

                // 初始化进度条和状态
                if (elements.progress.bar) elements.progress.bar.style.display = 'block';
                if (elements.progress.element) {
                    elements.progress.element.style.width = '0%';
                    elements.progress.element.className = 'progress';
                }
                if (elements.status) {
                    elements.status.style.display = 'block';
                    elements.status.className = 'status migrating';
                    elements.status.innerHTML = '<span class="migrating-text">迁移中</span><div class="migrating-dots"><span></span><span></span><span></span></div>';
                }
                if (elements.errorLog) elements.errorLog.style.display = 'none';

                // 启动进度条动画
                const startTime = Date.now();
                const interval = setInterval(() => {
                    if (!elements.progress.element) return;
                    const elapsedTime = Date.now() - startTime;
                    const progress = Math.min((elapsedTime / 60000) * 99, 99);
                    elements.progress.element.style.width = `${progress}%`;
                }, 100);

                try {
                    const response = await fetch('/project/sync', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify([{
                            jira_project_id: project.jira_project_id,
                            magic_library_id: project.magic_library_id
                        }])
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    // 获取更新后的项目数据
                    const updatedProjects = await response.json();
                    
                    // 更新本地状态和UI
                    updatedProjects.forEach(updatedProject => {
                        const index = state.projects.findIndex(p => p.jira_project_id === updatedProject.jira_project_id);
                        if (index !== -1) {
                            // 更新项目数据
                            state.projects[index] = {
                                ...state.projects[index],
                                ...updatedProject
                            };
                            
                            // 更新进度条为完成状态
                            if (elements.progress.element) {
                                elements.progress.element.style.width = '100%';
                                elements.progress.element.className = 'progress success';
                            }
                            
                            // 更新状态文本
                            if (elements.status) {
                                elements.status.className = 'status success';
                                elements.status.textContent = '迁移完成';
                            }

                            // 更新UI
                            view.updateProjectUI(state.projects[index], { project: elements.project });
                        }
                    });
                    
                    // 更新统计数据
                    view.updateStats();

                } catch (error) {
                    console.error('Migration failed:', error);
                    // 更新进度条为错误状态
                    if (elements.progress.element) {
                        elements.progress.element.style.width = '100%';
                        elements.progress.element.className = 'progress error';
                    }
                    if (elements.status) {
                        elements.status.className = 'status error';
                        elements.status.textContent = '迁移失败';
                    }
                    if (elements.errorLog) {
                        elements.errorLog.style.display = 'block';
                        elements.errorLog.textContent = error.message;
                    }
                } finally {
                    // 清除进度条动画
                    clearInterval(interval);
                    // 恢复当前项目的迁移按钮
                    elements.button.disabled = false;
                }
            },

            async startMigrateAll() {
                // 获取所有迁移按钮并禁用
                const allButtons = document.querySelectorAll('.button');
                allButtons.forEach(btn => btn.disabled = true);

                const projects = utils.filterProjects(state.searchQuery);
                const progressIntervals = new Map();

                try {
                    const syncRequests = projects.map(project => ({
                        jira_project_id: project.jira_project_id,
                        magic_library_id: project.magic_library_id
                    }));

                    // 为每个项目初始化进度条
                    projects.forEach(project => {
                        const projectElement = document.getElementById(`project-${project.id}`);
                        if (!projectElement) return;

                        const progressBar = projectElement.querySelector('.progress-bar');
                        const progressElement = projectElement.querySelector('.progress');
                        const statusElement = projectElement.querySelector('.status');
                        const errorLogElement = projectElement.querySelector('.error-log');

                        if (progressBar) progressBar.style.display = 'block';
                        if (progressElement) {
                            progressElement.style.width = '0%';
                            progressElement.className = 'progress';
                        }
                        if (statusElement) {
                            statusElement.style.display = 'block';
                            statusElement.className = 'status migrating';
                            statusElement.innerHTML = '<span class="migrating-text">迁移中</span><div class="migrating-dots"><span></span><span></span><span></span></div>';
                        }
                        if (errorLogElement) errorLogElement.style.display = 'none';

                        // 启动进度条动画
                        const startTime = Date.now();
                        const interval = setInterval(() => {
                            if (!progressElement) return;
                            const elapsedTime = Date.now() - startTime;
                            const progress = Math.min((elapsedTime / 60000) * 99, 99);
                            progressElement.style.width = `${progress}%`;
                        }, 100);

                        progressIntervals.set(project.id, interval);
                    });

                    // 发送批量同步请求
                    const response = await fetch('/project/sync', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(syncRequests)
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const result = await response.json();

                    // 更新每个项目的状态
                    projects.forEach(project => {
                        clearInterval(progressIntervals.get(project.id));

                        const projectElement = document.getElementById(`project-${project.id}`);
                        if (!projectElement) return;

                        const progressElement = projectElement.querySelector('.progress');
                        const statusElement = projectElement.querySelector('.status');
                        const errorLogElement = projectElement.querySelector('.error-log');

                        const updatedProject = result.find(p => p.jira_project_id === project.jira_project_id);
                        if (updatedProject) {
                            // 更新本地状态
                            const index = state.projects.findIndex(p => p.jira_project_id === updatedProject.jira_project_id);
                            if (index !== -1) {
                                state.projects[index] = {
                                    ...state.projects[index],
                                    ...updatedProject
                                };
                            }

                            if (progressElement) {
                                progressElement.style.width = '100%';
                                progressElement.className = 'progress success';
                            }
                            if (statusElement) {
                                statusElement.className = 'status success';
                                statusElement.textContent = '迁移完成';
                            }

                            view.updateProjectUI(state.projects[index], { project: projectElement });
                        } else {
                            if (progressElement) {
                                progressElement.style.width = '100%';
                                progressElement.className = 'progress error';
                            }
                            if (statusElement) {
                                statusElement.className = 'status error';
                                statusElement.textContent = '迁移失败';
                            }
                        }
                    });

                    view.updateStats();

                } catch (error) {
                    projects.forEach(project => {
                        clearInterval(progressIntervals.get(project.id));

                        const projectElement = document.getElementById(`project-${project.id}`);
                        if (!projectElement) return;

                        const progressElement = projectElement.querySelector('.progress');
                        const statusElement = projectElement.querySelector('.status');
                        const errorLogElement = projectElement.querySelector('.error-log');

                        if (progressElement) {
                            progressElement.style.width = '100%';
                            progressElement.className = 'progress error';
                        }
                        if (statusElement) {
                            statusElement.className = 'status error';
                            statusElement.textContent = '迁移失败';
                        }
                        if (errorLogElement) {
                            errorLogElement.style.display = 'block';
                            errorLogElement.textContent = `错误时间: ${new Date().toLocaleString()}\n错误详情: ${error.message}`;
                        }
                    });
                    console.error('Batch migration error:', error);
                } finally {
                    allButtons.forEach(btn => btn.disabled = false);
                    progressIntervals.forEach(interval => clearInterval(interval));
                }
            },

            init() {
                try {
                    // 获取后端数据
                    const config = JSON.parse(document.getElementById('backend-data').textContent.trim());
                    Object.assign(state, config);

                    // 添加搜索事件监听
                    document.getElementById('projectSearch')
                        .addEventListener('input', e => {
                            state.searchQuery = e.target.value.trim();
                            view.renderProjects();
                        });

                    // 添加全量迁移按钮事件监听
                    document.getElementById('migrateAllButton')
                        .addEventListener('click', () => controller.startMigrateAll());

                    view.renderProjects();
                } catch (error) {
                    document.getElementById('projectList').innerHTML = `
                        <div style="color: var(--error-color); padding: 20px; text-align: center;">
                            <i class="bi bi-exclamation-triangle-fill"></i>
                            数据加载失败: ${error.message}
                        </div>
                    `;
                }
            }
        };

        window.onload = controller.init;
    </script>
</body>
</html>
