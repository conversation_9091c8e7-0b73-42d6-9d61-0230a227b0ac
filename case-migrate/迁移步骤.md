## 1 jira 表数据
### 1 查询所有的项目
```sql
select * from project where pname in ('一本计划 2.0 平台项目','上飞二期','EICD协同开发平台','CAS告警信息系统',
'统一数据运营服务系统实施项目（一期）','企业标准数字化管控平台实施项目','2023年型号构型实物数据资产架构管理及生成功能优化项目'
,'翔运信息化平台建设','尚廉杰1.0','人力资源轻量化模块化软件研发项目','上飞公司维格斯实施','档案管理系统升级换版项目','数字档案馆建设') ;
```
### 2 查询项目下所有的用例及树状结构 项目 13004 为 demo

- ao_69e499_testsuitemember 描述树形结构，member_type: 0(用例节点) 1(用例集节点) 
- 如果 parent_id 不存在，则为根节点
```sql
select t1.*,t2.test_suite_id as praent_id from 
(select * from `jiraissue` where issuetype = 10412 and project= 13004 ) t1
left join ao_69e499_testsuitemember t2 on t1.id=t2.MEMBER_ID order by parent_id
```
### 3 查询项目下所有的用例集及树状结构
```sql
select t1.*,t2.test_suite_id as praent_id from 
(select * from ao_69e499_testsuite where PROJECT_ID =13004 ) t1
left join ao_69e499_testsuitemember t2 on t1.id=t2.MEMBER_ID ;
```
### 4 查询用例下的的步骤,id 是用例的id
```sql
select * from ao_69e499_teststep where tc_id=84519
```
---
## 2 magic mars 表数据
### 1 创建用例库
- 用例库名字是项目名

设计到的表
```sql
-- 保存所有的用例节点，parent_node_id 是 mars_nodes_hierarchy的 id
select * from mars_case where mars_case.library_id =5360 and mars_case.is_deleted ='N'
and name='保险商城-登录';

-- 保存所有的树节点 叶子和非叶子节点
select * from mars_nodes_hierarchy mnh where id=101403809;

select * from mars_nodes_hierarchy where parent_id='101403809';

select * from mars_case_step where case_id=190939;
```