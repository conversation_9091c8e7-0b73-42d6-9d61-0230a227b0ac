package magic

import "time"

// BaseModel 基础模型字段
type BaseModel struct {
	ID          int64     `gorm:"primarykey"`
	Creator     string    `json:"creator" yaml:"creator" gorm:"column:creator"`
	Modifier    string    `json:"modifier" yaml:"modifier" gorm:"column:modifier"`
	GmtCreated  time.Time `json:"gmt_created" yaml:"gmt_created" gorm:"column:gmt_created"`
	GmtModified time.Time `json:"gmt_modified" yaml:"gmt_modified" gorm:"column:gmt_modified"`
	IsDeleted   string    `json:"is_deleted" yaml:"is_deleted" gorm:"column:is_deleted"`
}

func (m *BaseModel) WithInit(user string) {
	if user != "" {
		m.Creator = user
		m.Modifier = user
	}
	m.IsDeleted = "N"
	m.GmtCreated = time.Now()
	m.GmtModified = time.Now()
}

// CaseLibrary 用例库
type CaseLibrary struct {
	BaseModel
	Name        string `json:"name" yaml:"name" gorm:"column:name"`
	Description string `json:"description" yaml:"description" gorm:"column:description"`
	SpaceId     int64  `json:"space_id" yaml:"space_id" gorm:"column:space_id"`
	Share       int    `json:"share" yaml:"share" gorm:"column:share"`
}

func (CaseLibrary) TableName() string {
	return "mars_case_library"
}

// MarsCase 测试用例
type MarsCase struct {
	BaseModel
	Name         string `json:"name" yaml:"name" gorm:"column:name"`
	Priority     int    `json:"priority" yaml:"priority" gorm:"column:priority"` // p3:4 p2:3 p1:2 p0:1
	Review       int    `json:"review" yaml:"review" gorm:"column:review"`       // 5:未评审 6:通过 7:不通过
	Sort         int    `json:"sort" yaml:"sort" gorm:"column:sort"`
	ParentNodeID int64  `json:"parent_node_id" gorm:"parent_node_id"`
	LibraryId    int64  `json:"library_id" gorm:"library_id"`
	// jira case id
	JiraCaseId int64 `json:"-" gorm:"-"`
}

func (MarsCase) TableName() string {
	return "mars_case"
}

// MarsCaseStep 测试用例步骤
type MarsCaseStep struct {
	BaseModel
	CaseID          int64  `json:"case_id" gorm:"case_id"`
	Sort            int    `json:"sort" gorm:"sort"`
	Type            int    `json:"type" gorm:"type"` // 1:分步骤 2:文本
	ExpectedResults string `json:"expected_results" gorm:"expected_results"`
	Description     string `json:"description" gorm:"description"`
}

func (MarsCaseStep) TableName() string {
	return "mars_case_step"
}

type MarsNodesHierarchy struct {
	BaseModel
	LibraryId  int64  `json:"library_id" yaml:"library_id" gorm:"column:library_id"` // 用例库 id
	NodeName   string `json:"node_name" yaml:"node_name" gorm:"column:node_name"`
	Type       int    `json:"type" yaml:"type" gorm:"column:type"`                      // 1 根节点 3 目录 6 用例
	RelationId *int64 `json:"relation_id" yaml:"relation_id" gorm:"column:relation_id"` // 如果是用例节点，则不为空
	ParentId   int64  `json:"parent_id" yaml:"parent_id" gorm:"column:parent_id"`
	Sort       int    `json:"sort" yaml:"sort" gorm:"column:sort"`    // 同节点的排序
	Level      int    `json:"level" yaml:"level" gorm:"column:level"` // 在数上的层级
}

func (MarsNodesHierarchy) TableName() string {
	return "mars_nodes_hierarchy"
}
