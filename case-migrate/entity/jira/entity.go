package jira

// Suite 用例集
type Suite struct {
	Id        int64  `json:"id" gorm:"column:id"`
	Name      string `json:"name" gorm:"column:name"`
	ProjectId int64  `json:"project_id" gorm:"column:project_id"`
	Sequence  int    `json:"sequence" gorm:"column:sequence"`
	ParentId  int64  `json:"parent_id" yaml:"parent_id" gorm:"column:parent_id"`
}

func (Suite) TableName() string {
	return "ao_69e499_testsuite"
}

type Case struct {
	Id          int64  `json:"id" gorm:"column:id"`
	Project     int64  `json:"project" gorm:"column:project"`
	IssueType   int64  `json:"issue_type" gorm:"column:issuetype"`
	Summary     string `json:"summary" gorm:"column:summary"` // 对应 name
	Description string `json:"description" gorm:"column:description"`
	Priority    int    `json:"priority" gorm:"column:priority"`        // 级别
	IssueStatus int    `json:"issue_status" gorm:"column:issuestatus"` // 状态
	ParentId    int64  `json:"parent_id" yaml:"parent_id" gorm:"column:parent_id"`
}

func (Case) TableName() string {
	return "ao_69e499_testcase"
}

type Step struct {
	Id             int64  `json:"id" gorm:"column:id"`
	TcId           int64  `json:"TcId" gorm:"column:tc_id"`
	Step           string `json:"step" gorm:"column:step"`
	ExpectedResult string `json:"expected_result" gorm:"column:expected_result"`
	Sequence       int    `json:"sequence" gorm:"column:sequence"` // 顺序 0 1024 2048 3072
}

func (Step) TableName() string {
	return "ao_69e499_teststep"
}
