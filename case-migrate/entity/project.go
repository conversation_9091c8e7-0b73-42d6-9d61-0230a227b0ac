package entity

type Project struct {
	Id             int64  `json:"id" yaml:"id" gorm:"column:id"`
	Name           string `json:"name" yaml:"name" gorm:"column:pname"`
	JiraProjectId  int64  `json:"jira_project_id" yaml:"jira_project_id" gorm:"column:-" mapstructure:"jira_project_id"`
	MagicProjectId int64  `json:"magic_project_id" yaml:"magic_project_id" gorm:"column:-" mapstructure:"magic_project_id"`
	JiraCount      int    `json:"jira_count" yaml:"jira_count" gorm:"column:-" mapstructure:"jira_count"`
	MagicCount     int    `json:"magic_count" yaml:"magic_count" gorm:"column:-" mapstructure:"magic_count"`
	Owner          string `json:"owner" yaml:"owner" gorm:"column:-"`
	MagicLibraryId int64  `json:"magic_library_id" yaml:"magic_library_id" gorm:"column:library_id" mapstructure:"magic_library_id"`
}

func (Project) TableName() string {
	return "project"
}
