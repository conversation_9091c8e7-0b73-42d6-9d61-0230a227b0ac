package db

import (
	"JiraMigrate/configs"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"log"
	"os"
	"time"
)

var dbMap = make(map[string]*gorm.DB)

func GetDB(name string) (*gorm.DB, error) {
	db, ok := dbMap[name]
	if !ok {
		log.Fatalf("failed to get db %s", name)
	}
	return db, nil
}

func Init() {
	gormConfig := &gorm.Config{
		Logger: logger.New(
			log.New(os.Stdout, "\r\n", log.LstdFlags),
			logger.Config{
				SlowThreshold: time.Second,
				LogLevel:      logger.Silent, // Log level
				Colorful:      true,
			},
		),
	}

	db1, err := gorm.Open(mysql.Open(configs.Config.Jira.Dsn), gormConfig)
	if err != nil {
		log.Fatalf("failed to connect to database %s: %v", configs.Config.Jira.Name, err)
	}
	log.Println("init jira db success")

	db2, err := gorm.Open(mysql.Open(configs.Config.Magic.Dsn), gormConfig)
	if err != nil {
		log.Fatalf("failed to connect to database %s: %v", configs.Config.Magic.Name, err)
	}
	log.Println("init magic db success")

	dbMap[configs.Config.Jira.Name] = db1
	dbMap[configs.Config.Magic.Name] = db2
	log.Printf("init db success , %+v ", dbMap)
}
