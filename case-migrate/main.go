package main

import (
	"JiraMigrate/configs"
	"JiraMigrate/db"
	_ "JiraMigrate/docs"
	"JiraMigrate/router"
	"embed"
	"github.com/gin-contrib/cors" // Add this import
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"html/template"
	"io/fs"
	"log"
	"net/http"
)

//go:embed templates/*
var templatesFS embed.FS

//go:embed static/*
var staticFS embed.FS

// @title			API 文档
// @version		1.0
// @description	API文档
// @host			127.0.0.1:8080
// @basePath		/
func setupRouter() *gin.Engine {
	server := gin.Default()

	// Add CORS middleware
	server.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Authorization", "Content-Type"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
	}))

	// 设置模板
	templ := template.Must(template.New("").ParseFS(templatesFS, "templates/*"))
	server.SetHTMLTemplate(templ)

	// 设置静态文件
	if staticRoot, err := fs.Sub(staticFS, "static"); err != nil {
		log.Fatal(err)
	} else {
		server.StaticFS("/static", http.FS(staticRoot))
	}
	server.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	// 路由设置
	router.Init(server)
	// 初始化数据库
	db.Init()
	return server
}

func main() {
	configs.Init()

	if err := setupRouter().Run(":8080"); err != nil {
		log.Fatal(err)
	}
}
