package router

import (
	"JiraMigrate/server"
	"github.com/gin-gonic/gin"
)

var projectServer server.ProjectServer
var health server.Health
var indexServer server.IndexServer

func Init(server *gin.Engine) {

	// 路由设置
	server.GET("/", indexServer.Index)

	server.GET("/ping", health.Ping)
	project := server.Group("/project")

	project.GET("list", projectServer.GetProjects)

	project.POST("sync", projectServer.SyncProjects)
}
