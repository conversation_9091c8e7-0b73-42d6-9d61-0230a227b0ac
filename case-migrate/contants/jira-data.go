package contants

// Priority2magic jira priority 表数据
// magic  p3:4 p2:3 p1:2 p0:1
func Priority2magic(id int) int {
	data := map[int]int{
		1:     1, // "Highest"
		2:     2, //"High",
		3:     3, //"Medium",
		4:     4, //"Low",
		5:     4, //"Lowest",
		10000: 3, //"中",
		10001: 2, //"高",
		10002: 4, //"低",
	}
	v, ok := data[id]
	if !ok {
		return 4
	}
	return v
}

// ReviewStatus2magic jira issue type 表数据
// magic 5:未评审 6:通过 7:不通过
func ReviewStatus2magic(id int) int {
	data := map[int]int{
		10000: 5, //"待办",
		10001: 6, //"完成",
		10614: 6, //"已评审",
		11120: 7, //"不适用",
		11121: 6, //"执行通过",
	}
	v, ok := data[id]
	if !ok {
		return 5
	}
	return v
}
