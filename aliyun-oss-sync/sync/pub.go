package sync

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

type Pub struct {
	Bucket *oss.Bucket
}

// Start 启动程序，将 localDir 中的所有文件同步到 ossDir 中
func (p *Pub) Start(ossDir, localDir string) error {
	builtData, err := BuildOssData(localDir)
	if err != nil {
		return err
	}

	ossData, err := FetchOSSData(p.Bucket, ossDir)
	if err != nil {
		return err
	}

	diff := builtData.Diff(ossData)
	if err := p.ApplyDiff(ossDir, localDir, diff); err != nil {
		return err
	}

	if !diff.Empty() {
		if err := p.WriteOssData(ossDir, localDir); err != nil {
			fmt.Printf("pub: wirteOssData failed: %s -> %s\n", ossDir, localDir)
		} else {
			fmt.Printf("pub: wirteOssData success: %s -> %s\n", ossDir, localDir)
		}
	}

	return nil
}

func (p *Pub) WriteOssData(ossDir, localDir string) error {
	// Update both local and remote .oss files after each change
	builtData, err := BuildOssData(localDir)
	if err != nil {
		return err
	}

	// Serialize .oss file
	content, err := json.Marshal(builtData)
	if err != nil {
		return err
	}

	// Update local .oss file
	localOssPath := filepath.Join(localDir, ossDataFile)
	if err := os.WriteFile(localOssPath, content, 0644); err != nil {
		return err
	}

	// Upload .oss file to OSS
	err = p.Bucket.PutObject(filepath.Join(ossDir, ossDataFile), strings.NewReader(string(content)))
	if err != nil {
		os.Remove(localOssPath)
		return err
	}
	return err
}

// ApplyDiff 将根据本地与云端的 oss data 差异进行同步，包含新增，修改和删除
func (p *Pub) ApplyDiff(ossDir, localDir string, diff *SyncDiff) error {
	// Handle added files
	for _, filePath := range diff.Added {
		ossKey := filepath.Join(ossDir, filePath)
		content, err := os.ReadFile(filepath.Join(localDir, filePath))
		if err != nil {
			return fmt.Errorf("failed to read file %s: %v", filePath, err)
		}
		err = p.Bucket.PutObject(ossKey, strings.NewReader(string(content)))
		if err != nil {
			return fmt.Errorf("failed to upload added file %s: %v", filePath, err)
		}
		fmt.Printf("pub: added file:%s -> %s\n", filePath, ossKey)
	}

	// Handle updated files
	for _, filePath := range diff.Updated {
		ossKey := filepath.Join(ossDir, filePath)
		content, err := os.Open(filepath.Join(localDir, filePath))
		if err != nil {
			return fmt.Errorf("failed to read file %s: %v", filePath, err)
		}
		err = p.Bucket.PutObject(ossKey, content)
		if err != nil {
			content.Close()
			return fmt.Errorf("failed to update file %s: %v", filePath, err)
		}
		content.Close()
		fmt.Printf("pub: modified file:%s -> %s\n", filePath, ossKey)
	}

	// Handle deleted files
	for _, filePath := range diff.Deleted {
		ossKey := filepath.Join(ossDir, filePath)

		err := p.Bucket.DeleteObject(ossKey)
		if err != nil {
			return fmt.Errorf("failed to delete file %s: %v", filePath, err)
		}
		fmt.Printf("pub: delete file:%s -> %s\n", filePath, ossKey)
	}

	return nil
}
