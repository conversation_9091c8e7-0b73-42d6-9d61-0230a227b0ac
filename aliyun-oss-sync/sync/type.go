package sync

import (
	"bufio"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/bmatcuk/doublestar/v4"
)

const ossDataFile = ".oss"

type OssFile struct {
	MD5      string
	FilePath string
}

type OssData struct {
	Files map[string]string // filename -> md5
}

type SyncDiff struct {
	Added   []string
	Updated []string
	Deleted []string
}

func (s *SyncDiff) Empty() bool {
	return len(s.Added) == 0 && len(s.Updated) == 0 && len(s.Deleted) == 0
}

// Diff 比较两个SyncData，返回差异
func (s *OssData) Diff(other *OssData) *SyncDiff {
	diff := &SyncDiff{}

	// 检查新增和修改的文件
	for path, md5 := range s.Files {
		if otherMd5, exists := other.Files[path]; !exists {
			diff.Added = append(diff.Added, path)
		} else if md5 != otherMd5 {
			diff.Updated = append(diff.Updated, path)
		}
	}

	// 检查删除的文件
	for path := range other.Files {
		if _, exists := s.Files[path]; !exists {
			diff.Deleted = append(diff.Deleted, path)
		}
	}

	return diff
}

// loadGitignorePatterns loads and parses .gitignore patterns
func loadGitignorePatterns(localPath string) ([]string, error) {
	gitignorePath := filepath.Join(localPath, ".gitignore")
	if _, err := os.Stat(gitignorePath); os.IsNotExist(err) {
		return nil, nil
	}

	file, err := os.Open(gitignorePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var patterns []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" && !strings.HasPrefix(line, "#") {
			patterns = append(patterns, line)
		}
	}
	return patterns, scanner.Err()
}

// matchesGitignore checks if a path matches any gitignore pattern
func matchesGitignore(path string, patterns []string) (bool, error) {
	for _, pattern := range patterns {
		match, err := doublestar.Match(pattern, path)
		if err != nil {
			return false, err
		}
		if match {
			return true, nil
		}
	}
	return false, nil
}

// BuildOssData 遍历文件夹 localpath 生成 OssData
func BuildOssData(localPath string) (*OssData, error) {
	data := &OssData{
		Files: make(map[string]string),
	}

	// Load gitignore patterns
	patterns, err := loadGitignorePatterns(localPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load gitignore patterns: %v", err)
	}

	err = filepath.Walk(localPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories and .oss file
		if info.IsDir() || filepath.Base(path) == ossDataFile {
			return nil
		}

		// Check if file matches gitignore patterns
		relPath, err := filepath.Rel(localPath, path)
		if err != nil {
			return err
		}

		if patterns != nil {
			ignore, err := matchesGitignore(relPath, patterns)
			if err != nil {
				return fmt.Errorf("gitignore pattern matching error: %v", err)
			}
			if ignore {
				return nil
			}
		}

		// Calculate MD5 and add to data
		md5hash, err := fileMd5(path)
		if err != nil {
			return err
		}

		data.Files[relPath] = md5hash
		return nil
	})

	return data, err
}

func fileMd5(path string) (string, error) {

	// Calculate MD5
	file, err := os.Open(path)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}
	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}

func FetchOSSData(bucket *oss.Bucket, ossDir string) (*OssData, error) {
	// Ensure path ends with /
	if !strings.HasSuffix(ossDir, "/") {
		ossDir += "/"
	}

	// Get the .oss file from OSS
	// Get the .oss file from OSS
	object, err := bucket.GetObject(ossDir + ossDataFile)
	if err != nil {
		if strings.Contains(err.Error(), "The specified key does not exist") {
			return &OssData{}, nil
		}
		return nil, err
	}
	defer object.Close()

	// Read and parse the .oss file content
	content, err := io.ReadAll(object)
	if err != nil {
		return nil, fmt.Errorf("failed to read .oss file: %v", err)
	}

	var ossData OssData
	if err := json.Unmarshal(content, &ossData); err != nil {
		return nil, fmt.Errorf("failed to decode .oss file: %v", err)
	}

	return &ossData, nil
}

func WirteLocalOssData(localPath string, data *OssData) error {
	// Marshal data to JSON
	content, err := json.Marshal(data)
	if err != nil {
		return err
	}

	// Write to file
	ossFilePath := filepath.Join(localPath, ossDataFile)
	if err := os.WriteFile(ossFilePath, content, 0644); err != nil {
		return err
	}
	return nil
}
