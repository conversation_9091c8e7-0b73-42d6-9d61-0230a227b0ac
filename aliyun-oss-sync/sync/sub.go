package sync

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"io"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

type Sub struct {
	Bucket *oss.Bucket
}

func (s *Sub) Start(ossDir string, localDir string) error {
	if err := os.MkdirAll(localDir, 0775); err != nil {
		return err
	}
	return s.syncFromOss(ossDir, localDir)
}

func (s *Sub) syncFromOss(ossDir string, localDir string) error {
	ossData, err := FetchOSSData(s.Bucket, ossDir)
	if err != nil {
		return err
	}

	localData, err := ReadLocalOssData(localDir)
	if err != nil {
		return err
	}

	diff := ossData.Diff(localData)
	if err := s.ApplyDiff(localDir, ossDir, *diff); err != nil {
		return err
	}

	return WirteLocalOssData(localDir, ossData)

}

// ReadLocalOssData 读取本地的 localDir 中 OssData 文件
func ReadLocalOssData(localDir string) (*OssData, error) {
	ossFilePath := filepath.Join(localDir, ossDataFile)

	// Check if .oss file exists
	if _, err := os.Stat(ossFilePath); os.IsNotExist(err) {
		return &OssData{Files: make(map[string]string)}, nil
	}

	// Read .oss file
	content, err := os.ReadFile(ossFilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read .oss file: %v", err)
	}

	// Parse JSON
	var ossData OssData
	if err := json.Unmarshal(content, &ossData); err != nil {
		return nil, fmt.Errorf("failed to parse .oss file: %v", err)
	}

	return &ossData, nil
}

// ApplyDiff 根据远端的 .oss 与本地的  .oss 文件比较来将 oss 的文件同步到本地，包括新增，删除和修改
func (s *Sub) ApplyDiff(localDir string, ossDir string, diff SyncDiff) error {
	totalFiles := len(diff.Added) + len(diff.Updated)
	if totalFiles == 0 {
		return nil
	}

	currentFile := 0
	showProgress := func(action string, filePath string) {
		currentFile++
		fmt.Printf("\r[%d/%d] %s %s", currentFile, totalFiles, action, filePath)
		if currentFile == totalFiles {
			fmt.Println()
		}
	}

	// Handle added files
	for _, filePath := range diff.Added {

		// Create parent directories if needed
		fullPath := filepath.Join(localDir, filePath)
		dir := filepath.Dir(fullPath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %v", dir, err)
		}

		ossFilePath := filepath.Join(ossDir, filePath)

		// Download file from OSS
		object, err := s.Bucket.GetObject(ossFilePath)
		if err != nil {
			return fmt.Errorf("failed to download added file %s: %v", ossFilePath, err)
		}
		defer object.Close()

		content, err := io.ReadAll(object)
		if err != nil {
			return fmt.Errorf("failed to read added file %s: %v", ossFilePath, err)
		}

		if err := os.WriteFile(fullPath, content, 0644); err != nil {
			return fmt.Errorf("failed to write added file %s: %v", fullPath, err)
		}

		showProgress("Downloading", ossFilePath)
	}

	// Handle updated files
	for _, filePath := range diff.Updated {
		fullPath := filepath.Join(localDir, filePath)
		dir := filepath.Dir(fullPath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %v", dir, err)
		}

		ossFilePath := filepath.Join(ossDir, filePath)

		// Download file from OSS
		object, err := s.Bucket.GetObject(ossFilePath)
		if err != nil {
			return fmt.Errorf("failed to download updated file %s: %v", ossFilePath, err)
		}
		defer object.Close()

		content, err := io.ReadAll(object)
		if err != nil {
			return fmt.Errorf("failed to read updated file %s: %v", ossFilePath, err)
		}

		if err := os.WriteFile(fullPath, content, 0644); err != nil {
			return fmt.Errorf("failed to write updated file %s: %v", filePath, err)
		}

		showProgress("Updating", filePath)
	}

	// Handle deleted files
	for _, filePath := range diff.Deleted {
		fullPath := filepath.Join(localDir, filePath)
		if err := os.Remove(fullPath); err != nil && !os.IsNotExist(err) {
			return fmt.Errorf("failed to delete file %s: %v", filePath, err)
		}
	}

	return nil
}
