package main

import (
	"fmt"
	"os"
	"strings"

	"codebase.zhonganinfo.com/zainfo/cube-kits/aliyun-oss-sync/config"
	"codebase.zhonganinfo.com/zainfo/cube-kits/aliyun-oss-sync/sync"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

func main() {
	if len(os.Args) != 3 {
		fmt.Println("Usage: aliyun-oss-sync <source> <target>")
		fmt.Println("Example:")
		fmt.Println("  aliyun-oss-sync ./local oss://bucket/path")
		fmt.Println("  aliyun-oss-sync oss://bucket/path ./local")
		os.Exit(1)
	}

	source := os.Args[1]
	target := os.Args[2]

	// Load config
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}

	// Initialize OSS client
	client, err := oss.New(
		cfg.OSS.Endpoint,
		cfg.OSS.AccessKeyID,
		cfg.OSS.AccessKeySecret,
	)
	if err != nil {
		fmt.Printf("Failed to create OSS client: %v\n", err)
		os.Exit(1)
	}

	// Determine sync direction
	if strings.HasPrefix(source, "oss://") {
		// OSS -> Local (Sub mode)
		localPath := target
		bucketName, ossPath := ossPathSplit(source)
		bucket, err := client.Bucket(bucketName)
		if err != nil {
			fmt.Printf("Failed to get bucket: %v\n", err)
			os.Exit(1)
		}

		sub := &sync.Sub{
			Bucket: bucket,
		}

		if err := sub.Start(ossPath, localPath); err != nil {
			fmt.Printf("Failed to start sync (OSS -> Local): %v\n", err)
			os.Exit(1)
		}
	} else if strings.HasPrefix(target, "oss://") {
		// Local -> OSS (Pub mode)
		localPath := source

		bucketName, ossPath := ossPathSplit(target)
		bucket, err := client.Bucket(bucketName)
		if err != nil {
			fmt.Printf("Failed to get bucket: %v\n", err)
			os.Exit(1)
		}
		pub := &sync.Pub{
			Bucket: bucket,
		}

		if err := pub.Start(ossPath, localPath); err != nil {
			fmt.Printf("Failed to start sync (Local -> OSS): %v\n", err)
			os.Exit(1)
		}
	} else {
		fmt.Println("Invalid source/target combination")
		fmt.Println("One path must be local (./path) and one must be OSS (oss://bucket/path)")
		os.Exit(1)
	}
}

// ossPathSplit
func ossPathSplit(str string) (bucket, key string) {
	str = strings.TrimPrefix(str, "oss://")

	i := strings.Index(str, "/")
	return str[:i], str[i+1:]
}
