# 基于阿里云 OSS 的文件同步工具

本工具利用阿里云 OSS 实现跨网络文件同步，特别适用于两个隔离网络但都能访问阿里云 OSS 的场景。

## 功能特性

- **双向同步**: 支持本地与 OSS 之间的双向同步
- **高效差异对比**: 使用 MD5 校验仅同步变更文件
- **Gitignore 支持**: 自动忽略符合 .gitignore 规则的文件
- **可靠性**: 自动处理网络中断并重试失败操作
- **可配置**: 通过配置文件自定义同步行为
- **增量同步**: 仅同步变更部分，节省带宽和时间

## 安装指南

1. 编译项目:

   ```bash
   go build -o aliyun-oss-sync
   ```

## 配置说明

创建 `config/config.yaml` 文件并填写 OSS 凭证:

```yaml
oss:
  endpoint: "https://oss-your-region.aliyuncs.com"
  accessKeyId: "your-access-key"
  accessKeySecret: "your-secret-key"
```

## 使用说明

### 本地同步到 OSS
```bash
./aliyun-oss-sync /local/dir oss://bucket/dir
```

### OSS 同步到本地
```bash
./aliyun-oss-sync oss://bucket/path /local/dir
```

### Gitignore 支持
工具会自动识别本地目录中的 `.gitignore` 文件，符合规则的文件将被排除在同步操作之外。

## 工作原理

### 时序图

```plaintext
+-----------------+       +-----------------+       +-----------------+
|   本地文件系统   |       |      OSS 存储     |       |   本地文件系统   |
|   (发布者)      |       |                  |       |   (订阅者)      |
+--------+--------+       +--------+--------+       +--------+--------+
         |                         |                         |
         | 1. 构建 OSS Data        |                         |
         |------------------------>|                         |
         |                         |                         |
         | 2. 获取 OSS Data        |                         |
         |<------------------------|                         |
         |                         |                         |
         | 3. 计算差异             |                         |
         |                         |                         |
         | 4. 同步变更             |                         |
         |------------------------>|                         |
         |                         |                         |
         | 5. 更新 OSS Data        |                         |
         |------------------------>|                         |
         |                         |                         |
         |                         | 1. 获取 OSS Data        |
         |                         |<------------------------|
         |                         |                         |
         |                         | 2. 计算差异             |
         |                         |                         |
         |                         | 3. 同步变更             |
         |                         |------------------------>|
         |                         |                         |
         |                         | 4. 更新本地 OSS Data    |
         |                         |------------------------>|
+--------+--------+       +--------+--------+       +--------+--------+
|   本地文件系统   |       |      OSS 存储     |       |   本地文件系统   |
|   (发布者)      |       |                  |       |   (订阅者)      |
+-----------------+       +-----------------+       +-----------------+
```

### OSS Data 文件
`.oss` 文件存储了文件名与 MD5 的映射关系，用于快速比较文件差异。

### Pub 角色（发布者）
1. 从本地目录构建 OSS Data
2. 与远程 OSS Data 进行比较
3. 同步变更部分到 OSS
4. 更新 OSS Data

### Sub 角色（订阅者）
1. 拉取远程 OSS Data
2. 与本地 OSS Data 进行比较
3. 从 OSS 更新差异部分到本地

## 同步流程

1. **构建 OSS Data**: 创建本地文件的 MD5 校验清单
2. **获取 OSS Data**: 从 OSS 获取远程文件清单
3. **计算差异**: 对比本地与远程清单
4. **应用变更**:
   - 上传新增/修改的文件到 OSS
   - 下载新增/修改的文件到本地
   - 删除被移除的文件

## 错误处理

- 自动重试网络错误
- 记录详细的错误日志
- 支持断点续传
- 失败操作回滚机制

## 常见问题

**Q: 文件未同步**
A: 检查 .gitignore 规则和文件权限

**Q: 认证失败**
A: 验证 config.yaml 中的 OSS 凭证

## 开源协议

MIT 许可证
